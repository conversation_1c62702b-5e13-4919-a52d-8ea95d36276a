'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { features } from '@/lib/data';
import Card, { FeatureCard } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

export default function FeaturePreviewSection() {
  const [activeFeature, setActiveFeature] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);
  const intervalRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Start auto-rotation when visible
          intervalRef.current = setInterval(() => {
            setActiveFeature((prev) => (prev + 1) % features.length);
          }, 4000);
        } else {
          // Stop auto-rotation when not visible
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const handleFeatureClick = (index) => {
    setActiveFeature(index);
    // Reset auto-rotation timer
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = setInterval(() => {
        setActiveFeature((prev) => (prev + 1) % features.length);
      }, 4000);
    }
  };

  return (
    <section ref={sectionRef} className="py-20 bg-surface relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, ${getComputedStyle(document.documentElement).getPropertyValue('--primary')} 0%, transparent 50%), 
                           radial-gradient(circle at 75% 75%, ${getComputedStyle(document.documentElement).getPropertyValue('--secondary')} 0%, transparent 50%)`,
          backgroundSize: '100px 100px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">Powerful Features</span>
            <br />
            <span className="text-text">Built for the Future</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto">
            Discover the cutting-edge capabilities that make NameCardAI the most advanced 
            digital business card platform in the world.
          </p>
        </div>

        {/* Feature Showcase */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Feature List */}
          <div className="space-y-4">
            {features.map((feature, index) => (
              <div
                key={feature.id}
                className={cn(
                  "p-6 rounded-xl cursor-pointer transition-all duration-500 border",
                  activeFeature === index
                    ? "glass neon-border glow-primary scale-105"
                    : "bg-surface-light border-surface-light hover:border-primary/50",
                  isVisible 
                    ? "opacity-100 translate-x-0" 
                    : "opacity-0 -translate-x-10"
                )}
                style={{ transitionDelay: `${index * 100}ms` }}
                onClick={() => handleFeatureClick(index)}
              >
                <div className="flex items-center space-x-4">
                  <div className={cn(
                    "text-3xl p-3 rounded-lg transition-all duration-300",
                    activeFeature === index
                      ? "bg-gradient-to-r from-primary to-secondary"
                      : "bg-surface"
                  )}>
                    {feature.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className={cn(
                      "text-xl font-semibold mb-2 transition-colors duration-300",
                      activeFeature === index ? "text-gradient" : "text-text"
                    )}>
                      {feature.title}
                    </h3>
                    <p className="text-text-muted">
                      {feature.description}
                    </p>
                  </div>
                  <div className={cn(
                    "w-3 h-3 rounded-full transition-all duration-300",
                    activeFeature === index 
                      ? "bg-primary animate-pulse" 
                      : "bg-surface-light"
                  )} />
                </div>
              </div>
            ))}
          </div>

          {/* Feature Demo */}
          <div className="relative">
            <div className={cn(
              "transition-all duration-700",
              isVisible ? "opacity-100 scale-100" : "opacity-0 scale-95"
            )}>
              {/* Main Demo Container */}
              <div className="glass p-8 rounded-2xl neon-border glow-primary relative overflow-hidden">
                {/* Demo Content Based on Active Feature */}
                <div className="text-center space-y-6">
                  <div className="text-6xl mb-4 animate-float">
                    {features[activeFeature]?.icon}
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gradient">
                    {features[activeFeature]?.title}
                  </h3>
                  
                  <p className="text-text-muted">
                    {features[activeFeature]?.description}
                  </p>

                  {/* Interactive Demo Elements */}
                  <div className="mt-8">
                    {activeFeature === 0 && (
                      // AR Card Demo
                      <div className="relative">
                        <div className="w-64 h-40 mx-auto bg-gradient-to-r from-primary/20 to-secondary/20 rounded-xl border border-primary/50 flex items-center justify-center animate-pulse-glow">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-2"></div>
                            <div className="text-sm text-text">AR Card Preview</div>
                          </div>
                        </div>
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-accent rounded-full animate-pulse"></div>
                        <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-secondary rounded-full animate-float"></div>
                      </div>
                    )}

                    {activeFeature === 1 && (
                      // Sharing Methods Demo
                      <div className="grid grid-cols-2 gap-4">
                        {['QR', 'NFC', '📷', '👤'].map((method, i) => (
                          <div key={i} className="p-4 bg-surface rounded-lg border border-primary/30 text-center animate-pulse-glow" style={{ animationDelay: `${i * 200}ms` }}>
                            <div className="text-2xl mb-1">{method}</div>
                            <div className="text-xs text-text-muted">Share Method</div>
                          </div>
                        ))}
                      </div>
                    )}

                    {activeFeature === 2 && (
                      // Face Recognition Demo
                      <div className="relative">
                        <div className="w-32 h-32 mx-auto bg-gradient-to-r from-accent/20 to-primary/20 rounded-full border-2 border-accent flex items-center justify-center">
                          <span className="text-3xl">👤</span>
                        </div>
                        <div className="absolute inset-0 border-2 border-accent rounded-full animate-ping"></div>
                        <div className="mt-4 text-sm text-accent">Face Recognition Active</div>
                      </div>
                    )}

                    {activeFeature === 3 && (
                      // Effects Gallery Demo
                      <div className="grid grid-cols-3 gap-2">
                        {['✨', '🌟', '💫', '🎆', '🔥', '⚡'].map((effect, i) => (
                          <div key={i} className="p-3 bg-surface rounded-lg border border-secondary/30 text-center hover:scale-110 transition-transform cursor-pointer">
                            <div className="text-xl">{effect}</div>
                          </div>
                        ))}
                      </div>
                    )}

                    {activeFeature === 4 && (
                      // Browser Demo
                      <div className="space-y-3">
                        <div className="flex justify-center space-x-4">
                          {['🌐', '📱', '💻', '⌚'].map((device, i) => (
                            <div key={i} className="w-12 h-12 bg-surface rounded-lg border border-primary/30 flex items-center justify-center animate-bounce" style={{ animationDelay: `${i * 100}ms` }}>
                              <span className="text-lg">{device}</span>
                            </div>
                          ))}
                        </div>
                        <div className="text-sm text-primary">Works on all devices</div>
                      </div>
                    )}

                    {activeFeature === 5 && (
                      // Analytics Demo
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="text-2xl font-bold text-gradient">1.2K</div>
                            <div className="text-xs text-text-muted">Views</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-gradient">89%</div>
                            <div className="text-xs text-text-muted">Engagement</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-gradient">156</div>
                            <div className="text-xs text-text-muted">Contacts</div>
                          </div>
                        </div>
                        <div className="h-2 bg-surface rounded-full overflow-hidden">
                          <div className="h-full bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse" style={{ width: '89%' }}></div>
                        </div>
                      </div>
                    )}
                  </div>

                  <Button variant="outline" size="sm">
                    Try {features[activeFeature]?.title}
                  </Button>
                </div>

                {/* Progress Indicator */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {features.map((_, index) => (
                    <div
                      key={index}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all duration-300 cursor-pointer",
                        activeFeature === index 
                          ? "bg-primary w-8" 
                          : "bg-surface-light hover:bg-primary/50"
                      )}
                      onClick={() => handleFeatureClick(index)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={feature.id}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              className={cn(
                "transition-all duration-700",
                isVisible 
                  ? "opacity-100 translate-y-0" 
                  : "opacity-0 translate-y-10"
              )}
              style={{ transitionDelay: `${index * 150}ms` }}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
