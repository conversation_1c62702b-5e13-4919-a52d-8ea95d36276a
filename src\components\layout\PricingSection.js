'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { pricingPlans } from '@/lib/data';
import { PricingCard } from '@/components/ui/Card';
import Button, { GradientButton } from '@/components/ui/Button';

export default function PricingSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [billingCycle, setBillingCycle] = useState('monthly');
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const getPrice = (plan) => {
    if (plan.id === 'free') return 0;
    return billingCycle === 'yearly' ? Math.floor(plan.price * 10) : plan.price;
  };

  const getPeriod = () => {
    return billingCycle === 'yearly' ? 'year' : 'month';
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-surface to-background relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        {/* Animated grid pattern */}
        <div className="absolute inset-0" style={{
          backgroundImage: `linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px), 
                           linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px)`,
          backgroundSize: '50px 50px',
          animation: 'matrix 20s linear infinite'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">Simple Pricing</span>
            <br />
            <span className="text-text">Powerful Results</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto mb-8">
            Choose the perfect plan for your networking needs. Start free and upgrade 
            as you grow your professional connections.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-12">
            <span className={cn(
              "text-lg font-medium transition-colors",
              billingCycle === 'monthly' ? "text-primary" : "text-text-muted"
            )}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={cn(
                "relative w-16 h-8 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                billingCycle === 'yearly' ? "bg-primary" : "bg-surface-light"
              )}
            >
              <div className={cn(
                "absolute top-1 w-6 h-6 bg-white rounded-full transition-transform duration-300",
                billingCycle === 'yearly' ? "translate-x-8" : "translate-x-1"
              )} />
            </button>
            <span className={cn(
              "text-lg font-medium transition-colors",
              billingCycle === 'yearly' ? "text-primary" : "text-text-muted"
            )}>
              Yearly
            </span>
            {billingCycle === 'yearly' && (
              <span className="bg-gradient-to-r from-primary to-secondary text-background px-3 py-1 rounded-full text-sm font-medium animate-pulse-glow">
                Save 20%
              </span>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingPlans.map((plan, index) => (
            <div
              key={plan.id}
              className={cn(
                "transition-all duration-700",
                isVisible 
                  ? "opacity-100 translate-y-0" 
                  : "opacity-0 translate-y-10",
                plan.popular && "md:-mt-4"
              )}
              style={{ transitionDelay: `${index * 200}ms` }}
            >
              <div className={cn(
                "relative h-full transition-all duration-300",
                plan.popular 
                  ? "glass neon-border glow-primary scale-105" 
                  : "glass hover:scale-105"
              )}>
                {/* Popular Badge */}
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-gradient-to-r from-primary to-secondary text-background px-6 py-2 rounded-full text-sm font-bold animate-pulse-glow">
                      🔥 Most Popular
                    </span>
                  </div>
                )}

                <div className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gradient mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-text-muted mb-6">
                      {plan.description}
                    </p>
                    
                    {/* Price */}
                    <div className="mb-6">
                      <div className="flex items-baseline justify-center">
                        <span className="text-5xl font-bold text-gradient">
                          ${getPrice(plan)}
                        </span>
                        <span className="text-text-muted ml-2">
                          /{getPeriod()}
                        </span>
                      </div>
                      {billingCycle === 'yearly' && plan.id !== 'free' && (
                        <div className="text-sm text-text-muted mt-2">
                          <span className="line-through">${plan.price * 12}</span>
                          <span className="text-primary ml-2">Save ${plan.price * 12 - getPrice(plan)}</span>
                        </div>
                      )}
                    </div>

                    {/* CTA Button */}
                    {plan.popular ? (
                      <GradientButton className="w-full mb-6">
                        {plan.cta}
                      </GradientButton>
                    ) : (
                      <Button 
                        variant={plan.id === 'free' ? 'outline' : 'default'} 
                        className="w-full mb-6"
                      >
                        {plan.cta}
                      </Button>
                    )}
                  </div>

                  {/* Features List */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-text mb-3">What's included:</h4>
                    <ul className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <span className="text-accent mr-3 mt-0.5 flex-shrink-0">✓</span>
                          <span className="text-text-muted text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {/* Limitations */}
                    {plan.limitations && plan.limitations.length > 0 && (
                      <div className="pt-4 border-t border-surface-light">
                        <ul className="space-y-2">
                          {plan.limitations.map((limitation, limitIndex) => (
                            <li key={limitIndex} className="flex items-start">
                              <span className="text-red-400 mr-3 mt-0.5 flex-shrink-0">×</span>
                              <span className="text-text-dim text-sm">{limitation}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gradient mb-6">
              All Plans Include
            </h3>
            
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl mb-2">🔒</div>
                <div className="font-semibold text-text mb-1">Secure</div>
                <div className="text-sm text-text-muted">Enterprise-grade security</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-2">🌐</div>
                <div className="font-semibold text-text mb-1">Global</div>
                <div className="text-sm text-text-muted">Works worldwide</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-2">📱</div>
                <div className="font-semibold text-text mb-1">Mobile</div>
                <div className="text-sm text-text-muted">All devices supported</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-2">💬</div>
                <div className="font-semibold text-text mb-1">Support</div>
                <div className="text-sm text-text-muted">24/7 assistance</div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Preview */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-text mb-4">
            Questions? We've got answers.
          </h3>
          <p className="text-text-muted mb-8">
            Check out our comprehensive FAQ or contact our support team.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline">
              <span className="mr-2">❓</span>
              View FAQ
            </Button>
            <Button variant="ghost">
              <span className="mr-2">💬</span>
              Contact Support
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
