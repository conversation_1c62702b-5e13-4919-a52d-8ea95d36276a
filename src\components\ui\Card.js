import { cn } from "@/lib/utils";

const cardVariants = {
  default: "bg-surface border border-surface-light",
  glass: "glass",
  gradient: "bg-gradient-to-br from-surface to-surface-light",
  neon: "bg-surface neon-border",
  floating: "bg-surface border border-surface-light shadow-2xl animate-float"
};

export default function Card({
  children,
  className,
  variant = "default",
  hover = true,
  glow = false,
  ...props
}) {
  return (
    <div
      className={cn(
        // Base styles
        "rounded-xl p-6 transition-all duration-300",
        
        // Hover effects
        hover && "hover:scale-105 hover:shadow-2xl cursor-pointer",
        
        // Glow effect
        glow && "glow-primary",
        
        // Variants
        cardVariants[variant],
        
        // Custom className
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardHeader({ children, className, ...props }) {
  return (
    <div
      className={cn("flex flex-col space-y-1.5 pb-4", className)}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardTitle({ children, className, ...props }) {
  return (
    <h3
      className={cn(
        "text-2xl font-semibold leading-none tracking-tight text-gradient",
        className
      )}
      {...props}
    >
      {children}
    </h3>
  );
}

export function CardDescription({ children, className, ...props }) {
  return (
    <p
      className={cn("text-sm text-text-muted", className)}
      {...props}
    >
      {children}
    </p>
  );
}

export function CardContent({ children, className, ...props }) {
  return (
    <div className={cn("pt-0", className)} {...props}>
      {children}
    </div>
  );
}

export function CardFooter({ children, className, ...props }) {
  return (
    <div
      className={cn("flex items-center pt-4", className)}
      {...props}
    >
      {children}
    </div>
  );
}

// Specialized card components
export function FeatureCard({ icon, title, description, className, ...props }) {
  return (
    <Card variant="glass" hover glow className={cn("text-center", className)} {...props}>
      <CardContent>
        <div className="text-4xl mb-4">{icon}</div>
        <CardTitle className="mb-2">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardContent>
    </Card>
  );
}

export function PricingCard({ 
  plan, 
  price, 
  period, 
  features, 
  popular = false, 
  className, 
  ...props 
}) {
  return (
    <Card 
      variant={popular ? "neon" : "glass"} 
      hover 
      glow={popular}
      className={cn(
        "relative h-full",
        popular && "scale-105 border-2 border-primary",
        className
      )} 
      {...props}
    >
      {popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-primary to-secondary text-background px-4 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}
      
      <CardHeader>
        <CardTitle>{plan}</CardTitle>
        <div className="text-3xl font-bold text-gradient">
          ${price}
          <span className="text-sm text-text-muted font-normal">/{period}</span>
        </div>
      </CardHeader>
      
      <CardContent>
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <span className="text-accent mr-2">✓</span>
              <span className="text-text-muted">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

export function TestimonialCard({ 
  name, 
  role, 
  company, 
  content, 
  avatar, 
  rating = 5,
  className, 
  ...props 
}) {
  return (
    <Card variant="glass" hover className={cn("h-full", className)} {...props}>
      <CardContent>
        <div className="flex items-center mb-4">
          {[...Array(rating)].map((_, i) => (
            <span key={i} className="text-accent text-lg">★</span>
          ))}
        </div>
        
        <p className="text-text-muted mb-6 italic">"{content}"</p>
        
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-background font-bold mr-4">
            {name.charAt(0)}
          </div>
          <div>
            <div className="font-semibold text-text">{name}</div>
            <div className="text-sm text-text-muted">{role} at {company}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
