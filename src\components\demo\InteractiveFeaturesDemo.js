'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import Button, { GradientButton } from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function InteractiveFeaturesDemo() {
  const [activeDemo, setActiveDemo] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [selectedColor, setSelectedColor] = useState('#00f5ff');
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [savedCards, setSavedCards] = useState([]);
  const sectionRef = useRef(null);

  const demos = [
    {
      id: 'drag-drop',
      name: 'Drag & Drop Customization',
      icon: '🎨',
      description: 'Intuitive drag and drop interface for easy customization'
    },
    {
      id: 'color-picker',
      name: 'Live Color Updates',
      icon: '🌈',
      description: 'Real-time color changes with instant preview'
    },
    {
      id: 'animation-controls',
      name: 'Animation Timeline',
      icon: '⏯️',
      description: 'Control animation speed and timing'
    },
    {
      id: 'save-load',
      name: 'Save & Load System',
      icon: '💾',
      description: 'Save your designs and load them anytime'
    }
  ];

  const colors = [
    '#00f5ff', '#ff0080', '#00ff41', '#ff6b35', '#7b68ee', 
    '#ff1493', '#00ced1', '#ffd700', '#ff4500', '#9370db'
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleDrag = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setDragPosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });
  };

  const saveCard = () => {
    const newCard = {
      id: Date.now(),
      name: `Design ${savedCards.length + 1}`,
      color: selectedColor,
      position: dragPosition,
      speed: animationSpeed,
      timestamp: new Date().toLocaleTimeString()
    };
    setSavedCards(prev => [...prev, newCard]);
  };

  const loadCard = (card) => {
    setSelectedColor(card.color);
    setDragPosition(card.position);
    setAnimationSpeed(card.speed);
  };

  const deleteCard = (cardId) => {
    setSavedCards(prev => prev.filter(card => card.id !== cardId));
  };

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-surface to-background relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `linear-gradient(45deg, var(--primary) 0%, transparent 25%), 
                           linear-gradient(-45deg, var(--secondary) 0%, transparent 25%)`,
          backgroundSize: '100px 100px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">Interactive Features</span>
            <br />
            <span className="text-text">Hands-On Experience</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto">
            Try out the powerful customization tools that make NameCardAI 
            the most user-friendly AR business card platform.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Demo Selection */}
          <div className={cn(
            "space-y-4 transition-all duration-700",
            isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-10"
          )}>
            <h3 className="text-xl font-bold text-gradient mb-6">
              Choose a Feature to Try
            </h3>

            {demos.map((demo, index) => (
              <button
                key={demo.id}
                onClick={() => setActiveDemo(index)}
                className={cn(
                  "w-full p-4 rounded-lg border transition-all duration-300 text-left",
                  activeDemo === index
                    ? "border-primary bg-primary/10 scale-105"
                    : "border-surface-light bg-surface hover:border-primary/50"
                )}
              >
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{demo.icon}</div>
                  <div>
                    <div className={cn(
                      "font-medium mb-1",
                      activeDemo === index ? "text-gradient" : "text-text"
                    )}>
                      {demo.name}
                    </div>
                    <div className="text-sm text-text-muted">
                      {demo.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Demo Area */}
          <div className={cn(
            "lg:col-span-2 transition-all duration-700",
            isVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-10"
          )}>
            <Card variant="glass" className="p-8 min-h-[600px]">
              {/* Drag & Drop Demo */}
              {activeDemo === 0 && (
                <div className="space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">Drag & Drop Interface</h4>
                  
                  <div 
                    className="relative w-full h-80 bg-gradient-to-br from-background to-surface border-2 border-dashed border-primary/30 rounded-lg cursor-crosshair overflow-hidden"
                    onMouseMove={handleDrag}
                  >
                    <div 
                      className="absolute w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-background font-bold text-xl transition-all duration-200 animate-pulse-glow"
                      style={{
                        left: `${dragPosition.x}%`,
                        top: `${dragPosition.y}%`,
                        transform: 'translate(-50%, -50%)'
                      }}
                    >
                      AI
                    </div>
                    
                    <div className="absolute bottom-4 left-4 text-sm text-text-muted">
                      Move your mouse to position the avatar
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-text-muted">
                      Position: X: {Math.round(dragPosition.x)}%, Y: {Math.round(dragPosition.y)}%
                    </p>
                  </div>
                </div>
              )}

              {/* Color Picker Demo */}
              {activeDemo === 1 && (
                <div className="space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">Live Color Customization</h4>
                  
                  <div className="text-center">
                    <div 
                      className="w-32 h-32 mx-auto rounded-full flex items-center justify-center text-white font-bold text-2xl transition-all duration-300 animate-pulse-glow"
                      style={{ backgroundColor: selectedColor }}
                    >
                      AI
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h5 className="font-semibold text-text">Choose a Color:</h5>
                    <div className="grid grid-cols-5 gap-3">
                      {colors.map((color) => (
                        <button
                          key={color}
                          onClick={() => setSelectedColor(color)}
                          className={cn(
                            "w-12 h-12 rounded-full border-2 transition-all duration-300 hover:scale-110",
                            selectedColor === color 
                              ? "border-white shadow-lg scale-110" 
                              : "border-transparent"
                          )}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    
                    <div className="mt-4">
                      <input
                        type="color"
                        value={selectedColor}
                        onChange={(e) => setSelectedColor(e.target.value)}
                        className="w-full h-12 rounded-lg border border-surface-light cursor-pointer"
                      />
                    </div>
                  </div>
                  
                  <div className="text-center text-sm text-text-muted">
                    Selected Color: {selectedColor}
                  </div>
                </div>
              )}

              {/* Animation Controls Demo */}
              {activeDemo === 2 && (
                <div className="space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">Animation Timeline Controls</h4>
                  
                  <div className="text-center">
                    <div 
                      className="w-32 h-32 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-background font-bold text-2xl"
                      style={{
                        animation: `float ${3 / animationSpeed}s ease-in-out infinite`
                      }}
                    >
                      AI
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h5 className="font-semibold text-text">Animation Speed:</h5>
                    <div className="space-y-2">
                      <input
                        type="range"
                        min="0.1"
                        max="3"
                        step="0.1"
                        value={animationSpeed}
                        onChange={(e) => setAnimationSpeed(parseFloat(e.target.value))}
                        className="w-full h-2 bg-surface rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-sm text-text-muted">
                        <span>Slow</span>
                        <span>Speed: {animationSpeed}x</span>
                        <span>Fast</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-3 mt-6">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setAnimationSpeed(0.5)}
                      >
                        Slow
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setAnimationSpeed(1)}
                      >
                        Normal
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setAnimationSpeed(2)}
                      >
                        Fast
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Save & Load Demo */}
              {activeDemo === 3 && (
                <div className="space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">Save & Load System</h4>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Current Design */}
                    <div className="space-y-4">
                      <h5 className="font-semibold text-text">Current Design:</h5>
                      <div className="p-4 bg-surface rounded-lg border border-surface-light">
                        <div className="text-center mb-4">
                          <div 
                            className="w-16 h-16 mx-auto rounded-full flex items-center justify-center text-white font-bold"
                            style={{ backgroundColor: selectedColor }}
                          >
                            AI
                          </div>
                        </div>
                        <div className="text-sm text-text-muted space-y-1">
                          <div>Color: {selectedColor}</div>
                          <div>Position: {Math.round(dragPosition.x)}%, {Math.round(dragPosition.y)}%</div>
                          <div>Speed: {animationSpeed}x</div>
                        </div>
                      </div>
                      
                      <GradientButton onClick={saveCard} className="w-full">
                        <span className="mr-2">💾</span>
                        Save Design
                      </GradientButton>
                    </div>
                    
                    {/* Saved Designs */}
                    <div className="space-y-4">
                      <h5 className="font-semibold text-text">Saved Designs ({savedCards.length}):</h5>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {savedCards.length === 0 ? (
                          <div className="text-center text-text-muted py-8">
                            No saved designs yet
                          </div>
                        ) : (
                          savedCards.map((card) => (
                            <div key={card.id} className="p-3 bg-surface rounded-lg border border-surface-light">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div 
                                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold"
                                    style={{ backgroundColor: card.color }}
                                  >
                                    AI
                                  </div>
                                  <div>
                                    <div className="font-medium text-text">{card.name}</div>
                                    <div className="text-xs text-text-muted">{card.timestamp}</div>
                                  </div>
                                </div>
                                <div className="flex space-x-2">
                                  <button
                                    onClick={() => loadCard(card)}
                                    className="text-primary hover:text-primary/80 text-sm"
                                  >
                                    Load
                                  </button>
                                  <button
                                    onClick={() => deleteCard(card.id)}
                                    className="text-red-400 hover:text-red-300 text-sm"
                                  >
                                    Delete
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <Card variant="glass" className="p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gradient mb-4">
              Ready to Create Your AR Business Card?
            </h3>
            <p className="text-text-muted mb-8">
              Experience all these features and more with your free NameCardAI account. 
              Start building your professional digital identity today.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GradientButton size="lg">
                <span className="mr-2">🚀</span>
                Start Creating Free
              </GradientButton>
              <Button variant="outline" size="lg">
                <span className="mr-2">📖</span>
                View Documentation
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
}
