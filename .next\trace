[{"name": "hot-reloader", "duration": 62, "timestamp": 44479705352, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748452110197, "traceId": "e1dd3c56424788a1"}, {"name": "setup-dev-bundler", "duration": 344329, "timestamp": 44479698062, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748452110189, "traceId": "e1dd3c56424788a1"}, {"name": "run-instrumentation-hook", "duration": 19, "timestamp": 44480072820, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748452110564, "traceId": "e1dd3c56424788a1"}, {"name": "start-dev-server", "duration": 723640, "timestamp": 44479355656, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "49858961408", "memory.totalMem": "68560273408", "memory.heapSizeLimit": "34481373184", "memory.rss": "167587840", "memory.heapTotal": "111906816", "memory.heapUsed": "57451392"}, "startTime": 1748452109847, "traceId": "e1dd3c56424788a1"}, {"name": "compile-path", "duration": 1297843, "timestamp": 44507150133, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748452137642, "traceId": "e1dd3c56424788a1"}, {"name": "ensure-page", "duration": 1298770, "timestamp": 44507149634, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748452137641, "traceId": "e1dd3c56424788a1"}]