'use client';

import { useState, useEffect } from 'react';
import Button, { GradientButton, OutlineButton } from '@/components/ui/Button';
import { cn } from '@/lib/utils';

export default function HeroSection() {
  const [typedText, setTypedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  
  const phrases = [
    'Your Name. Reinvented.',
    'Not Just a Card—An Experience.',
    'Connect in 3D. Remember Forever.',
    'The Future of Networking Is Here.'
  ];

  useEffect(() => {
    const currentPhrase = phrases[currentIndex];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        if (typedText.length < currentPhrase.length) {
          setTypedText(currentPhrase.slice(0, typedText.length + 1));
        } else {
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        if (typedText.length > 0) {
          setTypedText(typedText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % phrases.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [typedText, currentIndex, isDeleting, phrases]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-surface to-background">
        {/* Matrix Effect Background */}
        <div className="absolute inset-0 opacity-20">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute text-accent text-xs font-mono animate-matrix"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 20}s`,
                animationDuration: `${15 + Math.random() * 10}s`
              }}
            >
              {Math.random().toString(36).substring(2, 15)}
            </div>
          ))}
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-primary rounded-full animate-float opacity-60"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            {/* Main Headline */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold font-display">
                <span className="text-gradient">AR-Enhanced</span>
                <br />
                <span className="text-text">Digital Business</span>
                <br />
                <span className="text-gradient-secondary">Cards</span>
              </h1>
              
              {/* Typing Animation */}
              <div className="h-16 flex items-center justify-center lg:justify-start">
                <p className="text-xl md:text-2xl text-text-muted font-medium">
                  {typedText}
                  <span className="animate-pulse">|</span>
                </p>
              </div>
            </div>

            {/* Description */}
            <p className="text-lg md:text-xl text-text-muted max-w-2xl mx-auto lg:mx-0 leading-relaxed">
              Revolutionize professional networking with stunning, interactive profiles 
              shared via QR, NFC, facial recognition, or camera scan—
              <span className="text-primary font-semibold">no app required</span>.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <GradientButton size="lg" className="group">
                <span className="mr-2">🚀</span>
                Try Demo Now
                <svg
                  className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </svg>
              </GradientButton>
              
              <OutlineButton size="lg" className="group">
                <span className="mr-2">📹</span>
                Watch Video
                <svg
                  className="ml-2 w-5 h-5 group-hover:scale-110 transition-transform"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M8 5v14l11-7z" />
                </svg>
              </OutlineButton>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-surface-light">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-gradient">50K+</div>
                <div className="text-sm text-text-muted">Cards Created</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-gradient">12K+</div>
                <div className="text-sm text-text-muted">Active Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-gradient">250K+</div>
                <div className="text-sm text-text-muted">Connections</div>
              </div>
            </div>
          </div>

          {/* Right Column - 3D Card Preview */}
          <div className="relative">
            {/* 3D Card Container */}
            <div className="relative w-full max-w-md mx-auto">
              {/* Floating AR Card */}
              <div className="relative transform rotate-12 hover:rotate-0 transition-transform duration-700 group">
                <div className="glass p-8 rounded-2xl neon-border glow-primary animate-float">
                  {/* Card Content */}
                  <div className="text-center space-y-4">
                    {/* Avatar */}
                    <div className="w-20 h-20 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-2xl font-bold text-background">
                      AI
                    </div>
                    
                    {/* Name */}
                    <h3 className="text-xl font-bold text-gradient">Alex Thompson</h3>
                    <p className="text-text-muted">Senior Developer</p>
                    <p className="text-sm text-text-dim">TechFlow Inc.</p>
                    
                    {/* Contact Info */}
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-center space-x-2">
                        <span>📧</span>
                        <span className="text-text-muted"><EMAIL></span>
                      </div>
                      <div className="flex items-center justify-center space-x-2">
                        <span>📱</span>
                        <span className="text-text-muted">+1 (555) 123-4567</span>
                      </div>
                    </div>
                    
                    {/* AR Effect Indicator */}
                    <div className="pt-4 border-t border-surface-light">
                      <div className="flex items-center justify-center space-x-2 text-accent">
                        <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                        <span className="text-xs">AR Effect Active</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl -z-10 group-hover:blur-2xl transition-all duration-700"></div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-accent rounded-full animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-secondary rounded-full animate-float"></div>
              <div className="absolute top-1/2 -left-8 w-4 h-4 bg-primary rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
