"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./Billboard.cjs.js"),r=require("./ScreenSpace.cjs.js"),s=require("./ScreenSizer.cjs.js"),t=require("./QuadraticBezierLine.cjs.js"),o=require("./CubicBezierLine.cjs.js"),i=require("./CatmullRomLine.cjs.js"),a=require("./Line.cjs.js"),u=require("./PositionalAudio.cjs.js"),n=require("./Text.cjs.js"),c=require("./Text3D.cjs.js"),p=require("./Effects.cjs.js"),j=require("./GradientTexture.cjs.js"),x=require("./Image.cjs.js"),l=require("./Edges.cjs.js"),d=require("./Outlines.cjs.js"),q=require("./Trail.cjs.js"),m=require("./Sampler.cjs.js"),h=require("./ComputedAttribute.cjs.js"),C=require("./Clone.cjs.js"),M=require("./MarchingCubes.cjs.js"),S=require("./Decal.cjs.js"),T=require("./Svg.cjs.js"),b=require("./Gltf.cjs.js"),P=require("./AsciiRenderer.cjs.js"),f=require("./Splat.cjs.js"),g=require("./OrthographicCamera.cjs.js"),B=require("./PerspectiveCamera.cjs.js"),v=require("./CubeCamera.cjs.js"),A=require("./DeviceOrientationControls.cjs.js"),F=require("./FlyControls.cjs.js"),L=require("./MapControls.cjs.js"),G=require("./OrbitControls.cjs.js"),D=require("./TrackballControls.cjs.js"),E=require("./ArcballControls.cjs.js"),R=require("./TransformControls.cjs.js"),k=require("./PointerLockControls.cjs.js"),I=require("./FirstPersonControls.cjs.js"),w=require("./CameraControls.cjs.js"),z=require("./MotionPathControls.cjs.js"),O=require("./GizmoHelper.cjs.js"),y=require("./GizmoViewcube.cjs.js"),H=require("./GizmoViewport.cjs.js"),V=require("./Grid.cjs.js"),W=require("./CubeTexture.cjs.js"),K=require("./Fbx.cjs.js"),Q=require("./Ktx2.cjs.js"),N=require("./Progress.cjs.js"),U=require("./Texture.cjs.js"),X=require("./VideoTexture.cjs.js"),_=require("./useFont.cjs.js"),J=require("./useSpriteLoader.cjs.js"),Y=require("./Helper.cjs.js"),Z=require("./Stats.cjs.js"),$=require("./StatsGl.cjs.js"),ee=require("./useDepthBuffer.cjs.js"),re=require("./useAspect.cjs.js"),se=require("./useCamera.cjs.js"),te=require("./DetectGPU.cjs.js"),oe=require("./Bvh.cjs.js"),ie=require("./useContextBridge.cjs.js"),ae=require("./useAnimations.cjs.js"),ue=require("./Fbo.cjs.js"),ne=require("./useIntersect.cjs.js"),ce=require("./useBoxProjectedEnv.cjs.js"),pe=require("./BBAnchor.cjs.js"),je=require("./TrailTexture.cjs.js"),xe=require("./Example.cjs.js"),le=require("./SpriteAnimator.cjs.js"),de=require("./CurveModifier.cjs.js"),qe=require("./MeshDistortMaterial.cjs.js"),me=require("./MeshWobbleMaterial.cjs.js"),he=require("./MeshReflectorMaterial.cjs.js"),Ce=require("./MeshRefractionMaterial.cjs.js"),Me=require("./MeshTransmissionMaterial.cjs.js"),Se=require("./MeshDiscardMaterial.cjs.js"),Te=require("./MultiMaterial.cjs.js"),be=require("./PointMaterial.cjs.js"),Pe=require("./shaderMaterial.cjs.js"),fe=require("./softShadows.cjs.js"),ge=require("./shapes.cjs.js"),Be=require("./RoundedBox.cjs.js"),ve=require("./ScreenQuad.cjs.js"),Ae=require("./Center.cjs.js"),Fe=require("./Resize.cjs.js"),Le=require("./Bounds.cjs.js"),Ge=require("./CameraShake.cjs.js"),De=require("./Float.cjs.js"),Ee=require("./Stage.cjs.js"),Re=require("./Backdrop.cjs.js"),ke=require("./Shadow.cjs.js"),Ie=require("./Caustics.cjs.js"),we=require("./ContactShadows.cjs.js"),ze=require("./AccumulativeShadows.cjs.js"),Oe=require("./SpotLight.cjs.js"),ye=require("./Environment.cjs.js"),He=require("./Lightformer.cjs.js"),Ve=require("./Sky.cjs.js"),We=require("./Stars.cjs.js"),Ke=require("./Cloud.cjs.js"),Qe=require("./Sparkles.cjs.js"),Ne=require("./useEnvironment.cjs.js"),Ue=require("./MatcapTexture.cjs.js"),Xe=require("./NormalTexture.cjs.js"),_e=require("./Wireframe.cjs.js"),Je=require("./ShadowAlpha.cjs.js"),Ye=require("./Points.cjs.js"),Ze=require("./Instances.cjs.js"),$e=require("./Segments.cjs.js"),er=require("./Detailed.cjs.js"),rr=require("./Preload.cjs.js"),sr=require("./BakeShadows.cjs.js"),tr=require("./meshBounds.cjs.js"),or=require("./AdaptiveDpr.cjs.js"),ir=require("./AdaptiveEvents.cjs.js"),ar=require("./PerformanceMonitor.cjs.js"),ur=require("./RenderTexture.cjs.js"),nr=require("./RenderCubeTexture.cjs.js"),cr=require("./Mask.cjs.js"),pr=require("./Hud.cjs.js"),jr=require("./Fisheye.cjs.js"),xr=require("./MeshPortalMaterial.cjs.js"),lr=require("./calculateScaleFactor.cjs.js"),dr=require("camera-controls");function qr(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}require("@babel/runtime/helpers/extends"),require("react"),require("three"),require("@react-three/fiber"),require("three-stdlib"),require("troika-three-text"),require("suspend-react"),require("../helpers/constants.cjs.js"),require("meshline"),require("maath"),require("zustand"),require("hls.js"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("stats-gl"),require("detect-gpu"),require("three-mesh-bvh"),require("../helpers/deprecated.cjs.js"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js");var mr=qr(dr);exports.Billboard=e.Billboard,exports.ScreenSpace=r.ScreenSpace,exports.ScreenSizer=s.ScreenSizer,exports.QuadraticBezierLine=t.QuadraticBezierLine,exports.CubicBezierLine=o.CubicBezierLine,exports.CatmullRomLine=i.CatmullRomLine,exports.Line=a.Line,exports.PositionalAudio=u.PositionalAudio,exports.Text=n.Text,exports.Text3D=c.Text3D,exports.Effects=p.Effects,exports.isWebGL2Available=p.isWebGL2Available,exports.GradientTexture=j.GradientTexture,exports.GradientType=j.GradientType,exports.Image=x.Image,exports.Edges=l.Edges,exports.Outlines=d.Outlines,exports.Trail=q.Trail,exports.useTrail=q.useTrail,exports.Sampler=m.Sampler,exports.useSurfaceSampler=m.useSurfaceSampler,exports.ComputedAttribute=h.ComputedAttribute,exports.Clone=C.Clone,exports.MarchingCube=M.MarchingCube,exports.MarchingCubes=M.MarchingCubes,exports.MarchingPlane=M.MarchingPlane,exports.Decal=S.Decal,exports.Svg=T.Svg,exports.Gltf=b.Gltf,exports.useGLTF=b.useGLTF,exports.AsciiRenderer=P.AsciiRenderer,exports.Splat=f.Splat,exports.OrthographicCamera=g.OrthographicCamera,exports.PerspectiveCamera=B.PerspectiveCamera,exports.CubeCamera=v.CubeCamera,exports.useCubeCamera=v.useCubeCamera,exports.DeviceOrientationControls=A.DeviceOrientationControls,exports.FlyControls=F.FlyControls,exports.MapControls=L.MapControls,exports.OrbitControls=G.OrbitControls,exports.TrackballControls=D.TrackballControls,exports.ArcballControls=E.ArcballControls,exports.TransformControls=R.TransformControls,exports.PointerLockControls=k.PointerLockControls,exports.FirstPersonControls=I.FirstPersonControls,exports.CameraControls=w.CameraControls,exports.MotionPathControls=z.MotionPathControls,exports.useMotion=z.useMotion,exports.GizmoHelper=O.GizmoHelper,exports.useGizmoContext=O.useGizmoContext,exports.GizmoViewcube=y.GizmoViewcube,exports.GizmoViewport=H.GizmoViewport,exports.Grid=V.Grid,exports.CubeTexture=W.CubeTexture,exports.useCubeTexture=W.useCubeTexture,exports.Fbx=K.Fbx,exports.useFBX=K.useFBX,exports.Ktx2=Q.Ktx2,exports.useKTX2=Q.useKTX2,exports.Progress=N.Progress,exports.useProgress=N.useProgress,exports.IsObject=U.IsObject,exports.Texture=U.Texture,exports.useTexture=U.useTexture,exports.VideoTexture=X.VideoTexture,exports.useVideoTexture=X.useVideoTexture,exports.useFont=_.useFont,exports.checkIfFrameIsEmpty=J.checkIfFrameIsEmpty,exports.getFirstFrame=J.getFirstFrame,exports.useSpriteLoader=J.useSpriteLoader,exports.Helper=Y.Helper,exports.useHelper=Y.useHelper,exports.Stats=Z.Stats,exports.StatsGl=$.StatsGl,exports.useDepthBuffer=ee.useDepthBuffer,exports.useAspect=re.useAspect,exports.useCamera=se.useCamera,exports.DetectGPU=te.DetectGPU,exports.useDetectGPU=te.useDetectGPU,exports.Bvh=oe.Bvh,exports.useBVH=oe.useBVH,exports.useContextBridge=ie.useContextBridge,exports.useAnimations=ae.useAnimations,exports.Fbo=ue.Fbo,exports.useFBO=ue.useFBO,exports.useIntersect=ne.useIntersect,exports.useBoxProjectedEnv=ce.useBoxProjectedEnv,exports.BBAnchor=pe.BBAnchor,exports.TrailTexture=je.TrailTexture,exports.useTrailTexture=je.useTrailTexture,exports.Example=xe.Example,exports.SpriteAnimator=le.SpriteAnimator,exports.useSpriteAnimator=le.useSpriteAnimator,exports.CurveModifier=de.CurveModifier,exports.MeshDistortMaterial=qe.MeshDistortMaterial,exports.MeshWobbleMaterial=me.MeshWobbleMaterial,exports.MeshReflectorMaterial=he.MeshReflectorMaterial,exports.MeshRefractionMaterial=Ce.MeshRefractionMaterial,exports.MeshTransmissionMaterial=Me.MeshTransmissionMaterial,exports.MeshDiscardMaterial=Se.MeshDiscardMaterial,exports.MultiMaterial=Te.MultiMaterial,exports.PointMaterial=be.PointMaterial,exports.PointMaterialImpl=be.PointMaterialImpl,exports.shaderMaterial=Pe.shaderMaterial,exports.SoftShadows=fe.SoftShadows,exports.Box=ge.Box,exports.Capsule=ge.Capsule,exports.Circle=ge.Circle,exports.Cone=ge.Cone,exports.Cylinder=ge.Cylinder,exports.Dodecahedron=ge.Dodecahedron,exports.Extrude=ge.Extrude,exports.Icosahedron=ge.Icosahedron,exports.Lathe=ge.Lathe,exports.Octahedron=ge.Octahedron,exports.Plane=ge.Plane,exports.Polyhedron=ge.Polyhedron,exports.Ring=ge.Ring,exports.Shape=ge.Shape,exports.Sphere=ge.Sphere,exports.Tetrahedron=ge.Tetrahedron,exports.Torus=ge.Torus,exports.TorusKnot=ge.TorusKnot,exports.Tube=ge.Tube,exports.RoundedBox=Be.RoundedBox,exports.ScreenQuad=ve.ScreenQuad,exports.Center=Ae.Center,exports.Resize=Fe.Resize,exports.Bounds=Le.Bounds,exports.useBounds=Le.useBounds,exports.CameraShake=Ge.CameraShake,exports.Float=De.Float,exports.Stage=Ee.Stage,exports.Backdrop=Re.Backdrop,exports.Shadow=ke.Shadow,exports.Caustics=Ie.Caustics,exports.ContactShadows=we.ContactShadows,exports.AccumulativeShadows=ze.AccumulativeShadows,exports.RandomizedLight=ze.RandomizedLight,exports.accumulativeContext=ze.accumulativeContext,exports.SpotLight=Oe.SpotLight,exports.SpotLightShadow=Oe.SpotLightShadow,exports.Environment=ye.Environment,exports.EnvironmentCube=ye.EnvironmentCube,exports.EnvironmentMap=ye.EnvironmentMap,exports.EnvironmentPortal=ye.EnvironmentPortal,exports.Lightformer=He.Lightformer,exports.Sky=Ve.Sky,exports.calcPosFromAngles=Ve.calcPosFromAngles,exports.Stars=We.Stars,exports.Cloud=Ke.Cloud,exports.CloudInstance=Ke.CloudInstance,exports.Clouds=Ke.Clouds,exports.Sparkles=Qe.Sparkles,exports.useEnvironment=Ne.useEnvironment,exports.MatcapTexture=Ue.MatcapTexture,exports.useMatcapTexture=Ue.useMatcapTexture,exports.NormalTexture=Xe.NormalTexture,exports.useNormalTexture=Xe.useNormalTexture,exports.Wireframe=_e.Wireframe,exports.ShadowAlpha=Je.ShadowAlpha,exports.Point=Ye.Point,exports.Points=Ye.Points,exports.PointsBuffer=Ye.PointsBuffer,exports.PositionPoint=Ye.PositionPoint,exports.Instance=Ze.Instance,exports.InstancedAttribute=Ze.InstancedAttribute,exports.Instances=Ze.Instances,exports.Merged=Ze.Merged,exports.PositionMesh=Ze.PositionMesh,exports.createInstances=Ze.createInstances,exports.Segment=$e.Segment,exports.SegmentObject=$e.SegmentObject,exports.Segments=$e.Segments,exports.Detailed=er.Detailed,exports.Preload=rr.Preload,exports.BakeShadows=sr.BakeShadows,exports.meshBounds=tr.meshBounds,exports.AdaptiveDpr=or.AdaptiveDpr,exports.AdaptiveEvents=ir.AdaptiveEvents,exports.PerformanceMonitor=ar.PerformanceMonitor,exports.usePerformanceMonitor=ar.usePerformanceMonitor,exports.RenderTexture=ur.RenderTexture,exports.RenderCubeTexture=nr.RenderCubeTexture,exports.Mask=cr.Mask,exports.useMask=cr.useMask,exports.Hud=pr.Hud,exports.Fisheye=jr.Fisheye,exports.MeshPortalMaterial=xr.MeshPortalMaterial,exports.calculateScaleFactor=lr.calculateScaleFactor,Object.defineProperty(exports,"CameraControlsImpl",{enumerable:!0,get:function(){return mr.default}});
