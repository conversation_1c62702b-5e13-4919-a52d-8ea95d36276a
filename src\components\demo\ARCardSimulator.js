'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { demoCards, effectTypes } from '@/lib/data';
import Button, { GradientButton } from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function ARCardSimulator() {
  const [selectedCard, setSelectedCard] = useState(demoCards[0]);
  const [selectedEffect, setSelectedEffect] = useState(effectTypes[0]);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [cardData, setCardData] = useState(demoCards[0]);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleCardDataChange = (field, value) => {
    setCardData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEffectChange = (effect) => {
    setSelectedEffect(effect);
    setCardData(prev => ({
      ...prev,
      effect: effect.id
    }));
  };

  const exportCard = () => {
    // Simulate export functionality
    alert('Card exported successfully! (Demo simulation)');
  };

  return (
    <section ref={sectionRef} className="py-20 bg-surface relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-transparent to-secondary/20" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">AR Card Simulator</span>
            <br />
            <span className="text-text">Create Your Digital Identity</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto">
            Design and customize your AR-enhanced business card with real-time preview. 
            Experience the power of 3D animations and interactive effects.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Card Preview */}
          <div className={cn(
            "transition-all duration-700",
            isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-10"
          )}>
            <div className="sticky top-8">
              <h3 className="text-2xl font-bold text-gradient mb-6 text-center">
                Live Preview
              </h3>
              
              {/* 3D Card Container */}
              <div className="relative perspective-1000">
                <div className="relative transform-gpu transition-transform duration-700 hover:rotateY-12 hover:rotateX-5">
                  {/* Main Card */}
                  <div className={cn(
                    "relative w-full max-w-md mx-auto aspect-[1.6/1] rounded-2xl p-8 transition-all duration-500",
                    selectedEffect.id === 'matrix' && "bg-black/90 border border-accent",
                    selectedEffect.id === 'particles' && "bg-gradient-to-br from-primary/20 to-secondary/20 border border-primary",
                    selectedEffect.id === 'tilt' && "glass border border-white/20",
                    selectedEffect.id === 'glow' && "bg-surface neon-border glow-primary",
                    selectedEffect.id === 'hologram' && "bg-gradient-to-br from-accent/10 to-primary/10 border border-accent",
                    !['matrix', 'particles', 'tilt', 'glow', 'hologram'].includes(selectedEffect.id) && cardData.background
                  )}>
                    
                    {/* Effect Overlays */}
                    {selectedEffect.id === 'matrix' && (
                      <div className="absolute inset-0 overflow-hidden rounded-2xl">
                        {[...Array(20)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute text-accent text-xs font-mono animate-matrix opacity-60"
                            style={{
                              left: `${Math.random() * 100}%`,
                              animationDelay: `${Math.random() * 2}s`,
                              animationDuration: `${3 + Math.random() * 2}s`
                            }}
                          >
                            {Math.random().toString(36).substring(2, 8)}
                          </div>
                        ))}
                      </div>
                    )}

                    {selectedEffect.id === 'particles' && (
                      <div className="absolute inset-0 overflow-hidden rounded-2xl">
                        {[...Array(15)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-2 h-2 bg-primary rounded-full animate-float opacity-70"
                            style={{
                              left: `${Math.random() * 100}%`,
                              top: `${Math.random() * 100}%`,
                              animationDelay: `${Math.random() * 3}s`,
                              animationDuration: `${2 + Math.random() * 2}s`
                            }}
                          />
                        ))}
                      </div>
                    )}

                    {selectedEffect.id === 'hologram' && (
                      <div className="absolute inset-0 rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-accent/20 to-transparent animate-pulse" />
                        <div className="absolute top-0 left-0 right-0 h-1 bg-accent animate-pulse" />
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-accent animate-pulse" />
                      </div>
                    )}

                    {/* Card Content */}
                    <div className="relative z-10 h-full flex flex-col justify-between text-center">
                      {/* Avatar */}
                      <div className="flex justify-center mb-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-xl font-bold text-background">
                          {cardData.name.charAt(0)}
                        </div>
                      </div>
                      
                      {/* Name & Title */}
                      <div className="space-y-2 mb-4">
                        <h3 className="text-xl font-bold text-gradient">
                          {cardData.name}
                        </h3>
                        <p className="text-text-muted">{cardData.title}</p>
                        <p className="text-sm text-text-dim">{cardData.company}</p>
                      </div>
                      
                      {/* Contact Info */}
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-center space-x-2">
                          <span>📧</span>
                          <span className="text-text-muted">{cardData.email}</span>
                        </div>
                        <div className="flex items-center justify-center space-x-2">
                          <span>📱</span>
                          <span className="text-text-muted">{cardData.phone}</span>
                        </div>
                        <div className="flex items-center justify-center space-x-2">
                          <span>🌐</span>
                          <span className="text-text-muted">{cardData.website}</span>
                        </div>
                      </div>
                      
                      {/* AR Effect Indicator */}
                      <div className="pt-4 border-t border-surface-light">
                        <div className="flex items-center justify-center space-x-2 text-accent">
                          <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                          <span className="text-xs">{selectedEffect.name} Active</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl -z-10 animate-pulse-glow"></div>
                </div>
              </div>

              {/* Export Button */}
              <div className="text-center mt-8">
                <GradientButton onClick={exportCard} className="group">
                  <span className="mr-2">📸</span>
                  Export Card
                  <svg
                    className="ml-2 w-5 h-5 group-hover:scale-110 transition-transform"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </GradientButton>
              </div>
            </div>
          </div>

          {/* Customization Panel */}
          <div className={cn(
            "transition-all duration-700",
            isVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-10"
          )}>
            <div className="space-y-8">
              <h3 className="text-2xl font-bold text-gradient">
                Customize Your Card
              </h3>

              {/* Personal Information */}
              <Card variant="glass" className="p-6">
                <h4 className="text-lg font-semibold text-text mb-4">Personal Information</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-muted mb-2">Full Name</label>
                    <input
                      type="text"
                      value={cardData.name}
                      onChange={(e) => handleCardDataChange('name', e.target.value)}
                      className="w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-muted mb-2">Job Title</label>
                    <input
                      type="text"
                      value={cardData.title}
                      onChange={(e) => handleCardDataChange('title', e.target.value)}
                      className="w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-muted mb-2">Company</label>
                    <input
                      type="text"
                      value={cardData.company}
                      onChange={(e) => handleCardDataChange('company', e.target.value)}
                      className="w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                </div>
              </Card>

              {/* Contact Information */}
              <Card variant="glass" className="p-6">
                <h4 className="text-lg font-semibold text-text mb-4">Contact Information</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-muted mb-2">Email</label>
                    <input
                      type="email"
                      value={cardData.email}
                      onChange={(e) => handleCardDataChange('email', e.target.value)}
                      className="w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-muted mb-2">Phone</label>
                    <input
                      type="tel"
                      value={cardData.phone}
                      onChange={(e) => handleCardDataChange('phone', e.target.value)}
                      className="w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-muted mb-2">Website</label>
                    <input
                      type="url"
                      value={cardData.website}
                      onChange={(e) => handleCardDataChange('website', e.target.value)}
                      className="w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                </div>
              </Card>

              {/* AR Effects */}
              <Card variant="glass" className="p-6">
                <h4 className="text-lg font-semibold text-text mb-4">AR Effects</h4>
                <div className="grid grid-cols-2 gap-3">
                  {effectTypes.map((effect) => (
                    <button
                      key={effect.id}
                      onClick={() => handleEffectChange(effect)}
                      className={cn(
                        "p-4 rounded-lg border transition-all duration-300 text-left",
                        selectedEffect.id === effect.id
                          ? "border-primary bg-primary/10 text-primary"
                          : "border-surface-light bg-surface hover:border-primary/50 text-text-muted hover:text-text"
                      )}
                    >
                      <div className="font-medium mb-1">{effect.name}</div>
                      <div className="text-xs opacity-75">{effect.description}</div>
                    </button>
                  ))}
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
