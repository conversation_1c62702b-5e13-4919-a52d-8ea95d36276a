'use client';

import Link from 'next/link';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    product: [
      { name: 'Features', href: '/#features' },
      { name: 'Demo', href: '/demo' },
      { name: 'Pricing', href: '/#pricing' },
      { name: 'Roadmap', href: '/roadmap' },
      { name: 'API', href: '/api' }
    ],
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Why Us', href: '/why-us' },
      { name: 'Careers', href: '/careers' },
      { name: 'Press', href: '/press' },
      { name: 'Contact', href: '/contact' }
    ],
    resources: [
      { name: 'Help Center', href: '/help' },
      { name: 'Documentation', href: '/docs' },
      { name: 'Blog', href: '/blog' },
      { name: 'Community', href: '/community' },
      { name: 'Templates', href: '/templates' }
    ],
    legal: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Cookie Policy', href: '/cookies' },
      { name: 'GDPR', href: '/gdpr' },
      { name: 'Security', href: '/security' }
    ]
  };

  const socialLinks = [
    { name: 'Twitter', href: '#', icon: '🐦' },
    { name: 'LinkedIn', href: '#', icon: '💼' },
    { name: 'GitHub', href: '#', icon: '🐙' },
    { name: 'Discord', href: '#', icon: '💬' },
    { name: 'YouTube', href: '#', icon: '📺' }
  ];

  return (
    <footer className="bg-surface border-t border-surface-light relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 20% 80%, var(--primary) 0%, transparent 50%), 
                           radial-gradient(circle at 80% 20%, var(--secondary) 0%, transparent 50%)`,
          backgroundSize: '200px 200px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid lg:grid-cols-5 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              {/* Logo */}
              <Link href="/" className="flex items-center space-x-3 group mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300">
                  <svg
                    className="w-8 h-8 text-background"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                  </svg>
                </div>
                <span className="text-2xl font-bold font-display text-gradient">
                  NameCardAI
                </span>
              </Link>

              {/* Description */}
              <p className="text-text-muted mb-6 leading-relaxed">
                Revolutionizing professional networking with AR-enhanced digital business cards. 
                Share stunning, interactive profiles that work without apps and create lasting impressions.
              </p>

              {/* Newsletter Signup */}
              <div className="space-y-4">
                <h4 className="font-semibold text-text">Stay Updated</h4>
                <div className="flex space-x-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 bg-background border border-surface-light rounded-lg text-text placeholder-text-dim focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                  <Button variant="gradient" size="sm">
                    Subscribe
                  </Button>
                </div>
                <p className="text-xs text-text-dim">
                  Get the latest updates on new features and releases.
                </p>
              </div>
            </div>

            {/* Links Sections */}
            <div className="lg:col-span-3 grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Product */}
              <div>
                <h4 className="font-semibold text-text mb-4">Product</h4>
                <ul className="space-y-3">
                  {footerLinks.product.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-text-muted hover:text-primary transition-colors duration-300"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Company */}
              <div>
                <h4 className="font-semibold text-text mb-4">Company</h4>
                <ul className="space-y-3">
                  {footerLinks.company.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-text-muted hover:text-primary transition-colors duration-300"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Resources */}
              <div>
                <h4 className="font-semibold text-text mb-4">Resources</h4>
                <ul className="space-y-3">
                  {footerLinks.resources.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-text-muted hover:text-primary transition-colors duration-300"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Legal */}
              <div>
                <h4 className="font-semibold text-text mb-4">Legal</h4>
                <ul className="space-y-3">
                  {footerLinks.legal.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-text-muted hover:text-primary transition-colors duration-300"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="py-8 border-t border-surface-light">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-text-muted text-sm">
              © {currentYear} NameCardAI. All rights reserved. Built with ❤️ for the future of networking.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-6">
              <span className="text-text-muted text-sm">Follow us:</span>
              <div className="flex space-x-4">
                {socialLinks.map((social) => (
                  <Link
                    key={social.name}
                    href={social.href}
                    className="text-text-muted hover:text-primary transition-colors duration-300 hover:scale-110 transform"
                    title={social.name}
                  >
                    <span className="text-xl">{social.icon}</span>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="py-6 border-t border-surface-light">
          <div className="grid md:grid-cols-3 gap-6 text-center md:text-left">
            <div>
              <h5 className="font-semibold text-text mb-2">🌍 Global Reach</h5>
              <p className="text-text-muted text-sm">
                Available in 45+ countries with 24/7 support
              </p>
            </div>
            
            <div>
              <h5 className="font-semibold text-text mb-2">🔒 Enterprise Security</h5>
              <p className="text-text-muted text-sm">
                SOC 2 compliant with end-to-end encryption
              </p>
            </div>
            
            <div>
              <h5 className="font-semibold text-text mb-2">⚡ 99.9% Uptime</h5>
              <p className="text-text-muted text-sm">
                Reliable infrastructure with global CDN
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <div className="fixed bottom-8 right-8 z-50">
        <Button
          variant="gradient"
          size="lg"
          className="rounded-full w-16 h-16 shadow-2xl animate-pulse-glow"
          title="Get Help"
        >
          <span className="text-2xl">💬</span>
        </Button>
      </div>
    </footer>
  );
}
