{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Utility function to merge Tailwind CSS classes\n * @param {...string} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generate a random number between min and max\n * @param {number} min - Minimum value\n * @param {number} max - Maximum value\n * @returns {number} Random number\n */\nexport function randomBetween(min, max) {\n  return Math.random() * (max - min) + min;\n}\n\n/**\n * Debounce function to limit function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Time limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n/**\n * Format number with commas\n * @param {number} num - Number to format\n * @returns {string} Formatted number\n */\nexport function formatNumber(num) {\n  return new Intl.NumberFormat().format(num);\n}\n\n/**\n * Generate a unique ID\n * @returns {string} Unique ID\n */\nexport function generateId() {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * Check if device is mobile\n * @returns {boolean} True if mobile\n */\nexport function isMobile() {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n}\n\n/**\n * Check if device supports touch\n * @returns {boolean} True if touch supported\n */\nexport function isTouchDevice() {\n  if (typeof window === 'undefined') return false;\n  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n}\n\n/**\n * Smooth scroll to element\n * @param {string} elementId - ID of element to scroll to\n * @param {number} offset - Offset from top\n */\nexport function scrollToElement(elementId, offset = 0) {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Copy text to clipboard\n * @param {string} text - Text to copy\n * @returns {Promise<boolean>} Success status\n */\nexport async function copyToClipboard(text) {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (err) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n/**\n * Get random item from array\n * @param {Array} array - Array to pick from\n * @returns {*} Random item\n */\nexport function getRandomItem(array) {\n  return array[Math.floor(Math.random() * array.length)];\n}\n\n/**\n * Shuffle array\n * @param {Array} array - Array to shuffle\n * @returns {Array} Shuffled array\n */\nexport function shuffleArray(array) {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * Convert hex color to RGB\n * @param {string} hex - Hex color\n * @returns {object} RGB values\n */\nexport function hexToRgb(hex) {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n}\n\n/**\n * Validate email address\n * @param {string} email - Email to validate\n * @returns {boolean} Valid email\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Format date\n * @param {Date|string} date - Date to format\n * @param {string} locale - Locale for formatting\n * @returns {string} Formatted date\n */\nexport function formatDate(date, locale = 'en-US') {\n  return new Intl.DateTimeFormat(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(new Date(date));\n}\n\n/**\n * Calculate reading time\n * @param {string} text - Text to calculate reading time for\n * @param {number} wpm - Words per minute (default: 200)\n * @returns {number} Reading time in minutes\n */\nexport function calculateReadingTime(text, wpm = 200) {\n  const words = text.trim().split(/\\s+/).length;\n  return Math.ceil(words / wpm);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,cAAc,GAAG,EAAE,GAAG;IACpC,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;AACvC;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAOO,SAAS,aAAa,GAAG;IAC9B,OAAO,IAAI,KAAK,YAAY,GAAG,MAAM,CAAC;AACxC;AAMO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAMO,SAAS;IACd,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAMO,SAAS;IACd,uCAAmC;;IAAY;IAC/C,OAAO,kBAAkB,UAAU,UAAU,cAAc,GAAG;AAChE;AAOO,SAAS,gBAAgB,SAAS,EAAE,SAAS,CAAC;IACnD,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAOO,eAAe,gBAAgB,IAAI;IACxC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAOO,SAAS,cAAc,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAOO,SAAS,SAAS,GAAG;IAC1B,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,OAAO,SAAS;QACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IACzB,IAAI;AACN;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAQO,SAAS,WAAW,IAAI,EAAE,SAAS,OAAO;IAC/C,OAAO,IAAI,KAAK,cAAc,CAAC,QAAQ;QACrC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAQO,SAAS,qBAAqB,IAAI,EAAE,MAAM,GAAG;IAClD,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\nconst buttonVariants = {\n  default: \"bg-primary text-background hover:bg-primary/90 glow-primary\",\n  secondary: \"bg-secondary text-background hover:bg-secondary/90 glow-secondary\",\n  accent: \"bg-accent text-background hover:bg-accent/90 glow-accent\",\n  outline: \"border border-primary text-primary hover:bg-primary hover:text-background neon-border\",\n  ghost: \"text-primary hover:bg-primary/10 hover:text-primary\",\n  gradient: \"bg-gradient-to-r from-primary to-secondary text-background hover:from-primary/90 hover:to-secondary/90\",\n  glass: \"glass text-text hover:bg-white/20\"\n};\n\nconst buttonSizes = {\n  sm: \"h-9 px-3 text-sm\",\n  default: \"h-11 px-6 py-2\",\n  lg: \"h-12 px-8 text-lg\",\n  xl: \"h-14 px-10 text-xl\"\n};\n\nexport default function Button({\n  children,\n  className,\n  variant = \"default\",\n  size = \"default\",\n  disabled = false,\n  loading = false,\n  onClick,\n  type = \"button\",\n  ...props\n}) {\n  return (\n    <button\n      type={type}\n      className={cn(\n        // Base styles\n        \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300\",\n        \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\",\n        \"disabled:pointer-events-none disabled:opacity-50\",\n        \"transform hover:scale-105 active:scale-95\",\n        \n        // Variants\n        buttonVariants[variant],\n        \n        // Sizes\n        buttonSizes[size],\n        \n        // Loading state\n        loading && \"cursor-not-allowed opacity-70\",\n        \n        // Custom className\n        className\n      )}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"mr-2 h-4 w-4 animate-spin\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n}\n\n// Specialized button components\nexport function PrimaryButton({ children, ...props }) {\n  return (\n    <Button variant=\"default\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function SecondaryButton({ children, ...props }) {\n  return (\n    <Button variant=\"secondary\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function GradientButton({ children, ...props }) {\n  return (\n    <Button variant=\"gradient\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function OutlineButton({ children, ...props }) {\n  return (\n    <Button variant=\"outline\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function GlassButton({ children, ...props }) {\n  return (\n    <Button variant=\"glass\" {...props}>\n      {children}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,QAAQ;IACR,SAAS;IACT,OAAO;IACP,UAAU;IACV,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,OAAO,EAC7B,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,8FACA,0GACA,oDACA,6CAEA,WAAW;QACX,cAAc,CAAC,QAAQ,EAEvB,QAAQ;QACR,WAAW,CAAC,KAAK,EAEjB,gBAAgB;QAChB,WAAW,iCAEX,mBAAmB;QACnB;QAEF,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA9DwB;AAiEjB,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClD,qBACE,6LAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAChC;;;;;;AAGP;MANgB;AAQT,SAAS,gBAAgB,EAAE,QAAQ,EAAE,GAAG,OAAO;IACpD,qBACE,6LAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAClC;;;;;;AAGP;MANgB;AAQT,SAAS,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO;IACnD,qBACE,6LAAC;QAAO,SAAQ;QAAY,GAAG,KAAK;kBACjC;;;;;;AAGP;MANgB;AAQT,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClD,qBACE,6LAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAChC;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChD,qBACE,6LAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAC9B;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'Pitch', href: '/pitch' },\n    { name: 'Why Us', href: '/why-us' },\n    { name: 'Roadmap', href: '/roadmap' },\n  ];\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled \n          ? 'glass backdrop-blur-md border-b border-white/10' \n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300\">\n              <svg\n                className=\"w-6 h-6 text-background\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-bold font-display text-gradient\">\n              NameCardAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-text-muted hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full transition-all duration-300\" />\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"gradient\" size=\"sm\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2 rounded-lg text-text-muted hover:text-primary transition-colors\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMobileMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 glass backdrop-blur-md border-b border-white/10\">\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block text-text-muted hover:text-primary transition-colors duration-300 font-medium py-2\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 space-y-3\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">\n                  Sign In\n                </Button>\n                <Button variant=\"gradient\" size=\"sm\" className=\"w-full\">\n                  Get Started\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,oDACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;kDAER,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,6LAAC;oCAAK,WAAU;8CAA+C;;;;;;;;;;;;sCAMjE,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAW,MAAK;8CAAK;;;;;;;;;;;;sCAMvC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,oBAAoB,CAAC;sCAEpC,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,iCACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAAS;;;;;;kDAGrD,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAW,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GAlIwB;KAAA", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Button, { GradientButton, OutlineButton } from '@/components/ui/Button';\nimport { cn } from '@/lib/utils';\n\nexport default function HeroSection() {\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isDeleting, setIsDeleting] = useState(false);\n  \n  const phrases = [\n    'Your Name. Reinvented.',\n    'Not Just a Card—An Experience.',\n    'Connect in 3D. Remember Forever.',\n    'The Future of Networking Is Here.'\n  ];\n\n  useEffect(() => {\n    const currentPhrase = phrases[currentIndex];\n    const timeout = setTimeout(() => {\n      if (!isDeleting) {\n        if (typedText.length < currentPhrase.length) {\n          setTypedText(currentPhrase.slice(0, typedText.length + 1));\n        } else {\n          setTimeout(() => setIsDeleting(true), 2000);\n        }\n      } else {\n        if (typedText.length > 0) {\n          setTypedText(typedText.slice(0, -1));\n        } else {\n          setIsDeleting(false);\n          setCurrentIndex((prev) => (prev + 1) % phrases.length);\n        }\n      }\n    }, isDeleting ? 50 : 100);\n\n    return () => clearTimeout(timeout);\n  }, [typedText, currentIndex, isDeleting, phrases]);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Animated Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-background via-surface to-background\">\n        {/* Matrix Effect Background */}\n        <div className=\"absolute inset-0 opacity-20\">\n          {[...Array(50)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute text-accent text-xs font-mono animate-matrix\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 20}s`,\n                animationDuration: `${15 + Math.random() * 10}s`\n              }}\n            >\n              {Math.random().toString(36).substring(2, 15)}\n            </div>\n          ))}\n        </div>\n\n        {/* Floating Particles */}\n        <div className=\"absolute inset-0\">\n          {[...Array(20)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute w-2 h-2 bg-primary rounded-full animate-float opacity-60\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 3}s`,\n                animationDuration: `${3 + Math.random() * 2}s`\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Text Content */}\n          <div className=\"space-y-8\">\n            {/* Main Headline */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold font-display\">\n                <span className=\"text-gradient\">AR-Enhanced</span>\n                <br />\n                <span className=\"text-text\">Digital Business</span>\n                <br />\n                <span className=\"text-gradient-secondary\">Cards</span>\n              </h1>\n              \n              {/* Typing Animation */}\n              <div className=\"h-16 flex items-center justify-center lg:justify-start\">\n                <p className=\"text-xl md:text-2xl text-text-muted font-medium\">\n                  {typedText}\n                  <span className=\"animate-pulse\">|</span>\n                </p>\n              </div>\n            </div>\n\n            {/* Description */}\n            <p className=\"text-lg md:text-xl text-text-muted max-w-2xl mx-auto lg:mx-0 leading-relaxed\">\n              Revolutionize professional networking with stunning, interactive profiles \n              shared via QR, NFC, facial recognition, or camera scan—\n              <span className=\"text-primary font-semibold\">no app required</span>.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <GradientButton size=\"lg\" className=\"group\">\n                <span className=\"mr-2\">🚀</span>\n                Try Demo Now\n                <svg\n                  className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M13 7l5 5m0 0l-5 5m5-5H6\"\n                  />\n                </svg>\n              </GradientButton>\n              \n              <OutlineButton size=\"lg\" className=\"group\">\n                <span className=\"mr-2\">📹</span>\n                Watch Video\n                <svg\n                  className=\"ml-2 w-5 h-5 group-hover:scale-110 transition-transform\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M8 5v14l11-7z\" />\n                </svg>\n              </OutlineButton>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 pt-8 border-t border-surface-light\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-gradient\">50K+</div>\n                <div className=\"text-sm text-text-muted\">Cards Created</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-gradient\">12K+</div>\n                <div className=\"text-sm text-text-muted\">Active Users</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-gradient\">250K+</div>\n                <div className=\"text-sm text-text-muted\">Connections</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - 3D Card Preview */}\n          <div className=\"relative\">\n            {/* 3D Card Container */}\n            <div className=\"relative w-full max-w-md mx-auto\">\n              {/* Floating AR Card */}\n              <div className=\"relative transform rotate-12 hover:rotate-0 transition-transform duration-700 group\">\n                <div className=\"glass p-8 rounded-2xl neon-border glow-primary animate-float\">\n                  {/* Card Content */}\n                  <div className=\"text-center space-y-4\">\n                    {/* Avatar */}\n                    <div className=\"w-20 h-20 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-2xl font-bold text-background\">\n                      AI\n                    </div>\n                    \n                    {/* Name */}\n                    <h3 className=\"text-xl font-bold text-gradient\">Alex Thompson</h3>\n                    <p className=\"text-text-muted\">Senior Developer</p>\n                    <p className=\"text-sm text-text-dim\">TechFlow Inc.</p>\n                    \n                    {/* Contact Info */}\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex items-center justify-center space-x-2\">\n                        <span>📧</span>\n                        <span className=\"text-text-muted\"><EMAIL></span>\n                      </div>\n                      <div className=\"flex items-center justify-center space-x-2\">\n                        <span>📱</span>\n                        <span className=\"text-text-muted\">+****************</span>\n                      </div>\n                    </div>\n                    \n                    {/* AR Effect Indicator */}\n                    <div className=\"pt-4 border-t border-surface-light\">\n                      <div className=\"flex items-center justify-center space-x-2 text-accent\">\n                        <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                        <span className=\"text-xs\">AR Effect Active</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl -z-10 group-hover:blur-2xl transition-all duration-700\"></div>\n              </div>\n              \n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 w-8 h-8 bg-accent rounded-full animate-pulse\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-secondary rounded-full animate-float\"></div>\n              <div className=\"absolute top-1/2 -left-8 w-4 h-4 bg-primary rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-primary rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU;QACd;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,gBAAgB,OAAO,CAAC,aAAa;YAC3C,MAAM,UAAU;iDAAW;oBACzB,IAAI,CAAC,YAAY;wBACf,IAAI,UAAU,MAAM,GAAG,cAAc,MAAM,EAAE;4BAC3C,aAAa,cAAc,KAAK,CAAC,GAAG,UAAU,MAAM,GAAG;wBACzD,OAAO;4BACL;iEAAW,IAAM,cAAc;gEAAO;wBACxC;oBACF,OAAO;wBACL,IAAI,UAAU,MAAM,GAAG,GAAG;4BACxB,aAAa,UAAU,KAAK,CAAC,GAAG,CAAC;wBACnC,OAAO;4BACL,cAAc;4BACd;iEAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;;wBACvD;oBACF;gBACF;gDAAG,aAAa,KAAK;YAErB;yCAAO,IAAM,aAAa;;QAC5B;gCAAG;QAAC;QAAW;QAAc;QAAY;KAAQ;IAEjD,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,gBAAgB,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oCACxC,mBAAmB,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;gCAClD;0CAEC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;+BARpC;;;;;;;;;;kCAcX,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gCAChD;+BAPK;;;;;;;;;;;;;;;;0BAcb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;;;;;8DACD,6LAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,6LAAC;;;;;8DACD,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAI5C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDACV;kEACD,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,6LAAC;oCAAE,WAAU;;wCAA+E;sDAG1F,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAAsB;;;;;;;8CAIrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,iBAAc;4CAAC,MAAK;4CAAK,WAAU;;8DAClC,6LAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;8DAEhC,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;sDAKR,6LAAC,oIAAA,CAAA,gBAAa;4CAAC,MAAK;4CAAK,WAAU;;8DACjC,6LAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;8DAEhC,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;8DAER,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAMd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAE3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAE3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAEb,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEAAgJ;;;;;;sEAK/J,6LAAC;4DAAG,WAAU;sEAAkC;;;;;;sEAChD,6LAAC;4DAAE,WAAU;sEAAkB;;;;;;sEAC/B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEAGrC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAU;sFAAkB;;;;;;;;;;;;8EAEpC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAU;sFAAkB;;;;;;;;;;;;;;;;;;sEAKtC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOlC,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;GAvNwB;KAAA", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport Button, { GradientButton } from '@/components/ui/Button';\n\nexport default function ProblemSolutionSection() {\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const problems = [\n    {\n      icon: '📄',\n      title: 'Paper Cards Are Outdated',\n      description: 'Get lost, damaged, or thrown away within a week. 88% end up in trash.',\n      stat: '7B+ cards wasted yearly'\n    },\n    {\n      icon: '🔋',\n      title: 'Tech Dependency Issues',\n      description: 'QR/NFC cards rely on battery life, app compatibility, and device support.',\n      stat: '40% failure rate'\n    },\n    {\n      icon: '🤝',\n      title: 'Missed Connections',\n      description: 'Forgetting cards or low-tech limitations lead to lost networking opportunities.',\n      stat: '60% of contacts lost'\n    },\n    {\n      icon: '💼',\n      title: 'No Digital Engagement',\n      description: 'Physical cards offer zero interaction, analytics, or follow-up capabilities.',\n      stat: 'Zero insights'\n    }\n  ];\n\n  const solutions = [\n    {\n      icon: '🎯',\n      title: 'AR-Powered Cards',\n      description: 'Stunning 3D animated business cards with real-time AR effects that wow.',\n      benefit: 'Instant memorability'\n    },\n    {\n      icon: '📱',\n      title: 'Multiple Share Methods',\n      description: 'QR, NFC, camera scan, or just remember a name/number. Always accessible.',\n      benefit: 'Never miss a connection'\n    },\n    {\n      icon: '🌐',\n      title: 'No App Required',\n      description: 'Works directly in any web browser. Recipients need zero downloads.',\n      benefit: '100% compatibility'\n    },\n    {\n      icon: '📊',\n      title: 'Smart Analytics',\n      description: 'Track views, interactions, and networking success with detailed insights.',\n      benefit: 'Measurable ROI'\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gradient-to-b from-background to-surface relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-30\">\n        {/* Floating geometric shapes */}\n        {[...Array(15)].map((_, i) => (\n          <div\n            key={i}\n            className={cn(\n              \"absolute rounded-full opacity-20\",\n              i % 3 === 0 ? \"bg-primary\" : i % 3 === 1 ? \"bg-secondary\" : \"bg-accent\"\n            )}\n            style={{\n              width: `${20 + Math.random() * 40}px`,\n              height: `${20 + Math.random() * 40}px`,\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,\n              animationDelay: `${Math.random() * 2}s`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">The Problem</span>\n            <span className=\"text-text\"> vs </span>\n            <span className=\"text-gradient-secondary\">Our Solution</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto\">\n            Traditional networking is broken. We're fixing it with cutting-edge AR technology \n            that makes every interaction unforgettable.\n          </p>\n        </div>\n\n        {/* Problem vs Solution Grid */}\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-20\">\n          {/* Problems Side */}\n          <div className=\"space-y-8\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"text-3xl font-bold text-gradient mb-4 flex items-center justify-center lg:justify-start\">\n                <span className=\"mr-3\">⚠️</span>\n                Current Problems\n              </h3>\n              <p className=\"text-text-muted\">\n                Why traditional business cards and current digital solutions fail\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {problems.map((problem, index) => (\n                <div\n                  key={index}\n                  className={cn(\n                    \"glass p-6 rounded-xl border border-red-500/20 transition-all duration-700\",\n                    isVisible \n                      ? \"opacity-100 translate-x-0\" \n                      : \"opacity-0 -translate-x-10\"\n                  )}\n                  style={{ transitionDelay: `${index * 150}ms` }}\n                >\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"text-3xl\">{problem.icon}</div>\n                    <div className=\"flex-1\">\n                      <h4 className=\"text-xl font-semibold text-text mb-2\">\n                        {problem.title}\n                      </h4>\n                      <p className=\"text-text-muted mb-3\">\n                        {problem.description}\n                      </p>\n                      <div className=\"inline-block bg-red-500/20 text-red-400 px-3 py-1 rounded-full text-sm font-medium\">\n                        {problem.stat}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Solutions Side */}\n          <div className=\"space-y-8\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"text-3xl font-bold text-gradient-secondary mb-4 flex items-center justify-center lg:justify-start\">\n                <span className=\"mr-3\">✨</span>\n                Our Solutions\n              </h3>\n              <p className=\"text-text-muted\">\n                How NameCardAI revolutionizes professional networking\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {solutions.map((solution, index) => (\n                <div\n                  key={index}\n                  className={cn(\n                    \"glass p-6 rounded-xl neon-border glow-primary transition-all duration-700 hover:scale-105\",\n                    isVisible \n                      ? \"opacity-100 translate-x-0\" \n                      : \"opacity-0 translate-x-10\"\n                  )}\n                  style={{ transitionDelay: `${index * 150 + 200}ms` }}\n                >\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"text-3xl\">{solution.icon}</div>\n                    <div className=\"flex-1\">\n                      <h4 className=\"text-xl font-semibold text-text mb-2\">\n                        {solution.title}\n                      </h4>\n                      <p className=\"text-text-muted mb-3\">\n                        {solution.description}\n                      </p>\n                      <div className=\"inline-block bg-gradient-to-r from-primary/20 to-secondary/20 text-primary px-3 py-1 rounded-full text-sm font-medium\">\n                        {solution.benefit}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Comparison Stats */}\n        <div className=\"mt-20 text-center\">\n          <div className=\"glass p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-8\">\n              The Difference is Clear\n            </h3>\n            \n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-red-400 mb-2\">88%</div>\n                <div className=\"text-text-muted\">Paper cards thrown away</div>\n                <div className=\"text-sm text-red-400 mt-1\">Traditional</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-gradient mb-2\">70%</div>\n                <div className=\"text-text-muted\">Higher engagement with AR</div>\n                <div className=\"text-sm text-primary mt-1\">NameCardAI</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-gradient-secondary mb-2\">100%</div>\n                <div className=\"text-text-muted\">Browser compatibility</div>\n                <div className=\"text-sm text-accent mt-1\">No app needed</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"text-2xl font-bold text-text mb-4\">\n            Ready to revolutionize your networking?\n          </h3>\n          <p className=\"text-text-muted mb-8 max-w-2xl mx-auto\">\n            Join thousands of professionals who've already made the switch to AR-enhanced digital business cards.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <GradientButton size=\"lg\">\n              <span className=\"mr-2\">🚀</span>\n              Start Free Trial\n            </GradientButton>\n            <Button variant=\"outline\" size=\"lg\">\n              <span className=\"mr-2\">📊</span>\n              See Comparison\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,WAAW,IAAI;oDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;mDACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;oDAAO,IAAM,SAAS,UAAU;;QAClC;2CAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BAEZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oCACA,IAAI,MAAM,IAAI,eAAe,IAAI,MAAM,IAAI,iBAAiB;wBAE9D,OAAO;4BACL,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;4BACrC,QAAQ,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;4BACtC,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC9B,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK,EAAE,sBAAsB,CAAC;4BACjE,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wBACzC;uBAZK;;;;;;;;;;0BAiBX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;0CAE5C,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAO3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAO;;;;;;oDAAS;;;;;;;0DAGlC,6LAAC;gDAAE,WAAU;0DAAkB;;;;;;;;;;;;kDAKjC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6EACA,YACI,8BACA;gDAEN,OAAO;oDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gDAAC;0DAE7C,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAY,QAAQ,IAAI;;;;;;sEACvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,QAAQ,KAAK;;;;;;8EAEhB,6LAAC;oEAAE,WAAU;8EACV,QAAQ,WAAW;;;;;;8EAEtB,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI;;;;;;;;;;;;;;;;;;+CAnBd;;;;;;;;;;;;;;;;0CA6Bb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAO;;;;;;oDAAQ;;;;;;;0DAGjC,6LAAC;gDAAE,WAAU;0DAAkB;;;;;;;;;;;;kDAKjC,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;gDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA,YACI,8BACA;gDAEN,OAAO;oDAAE,iBAAiB,GAAG,QAAQ,MAAM,IAAI,EAAE,CAAC;gDAAC;0DAEnD,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAY,SAAS,IAAI;;;;;;sEACxC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,SAAS,KAAK;;;;;;8EAEjB,6LAAC;oEAAE,WAAU;8EACV,SAAS,WAAW;;;;;;8EAEvB,6LAAC;oEAAI,WAAU;8EACZ,SAAS,OAAO;;;;;;;;;;;;;;;;;;+CAnBlB;;;;;;;;;;;;;;;;;;;;;;kCA8Bf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAuC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAkB;;;;;;8DACjC,6LAAC;oDAAI,WAAU;8DAA4B;;;;;;;;;;;;sDAG7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAAkB;;;;;;8DACjC,6LAAC;oDAAI,WAAU;8DAA4B;;;;;;;;;;;;sDAG7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAkD;;;;;;8DACjE,6LAAC;oDAAI,WAAU;8DAAkB;;;;;;8DACjC,6LAAC;oDAAI,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAItD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,iBAAc;wCAAC,MAAK;;0DACnB,6LAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;kDAGlC,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,6LAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GA9PwB;KAAA", "debugId": null}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/lib/data.js"], "sourcesContent": ["// Mock data for NameCardAI MVP\n\nexport const features = [\n  {\n    id: 1,\n    title: \"AR Card Rendering\",\n    description: \"Stunning 3D animated business cards with real-time AR effects\",\n    icon: \"🎯\",\n    category: \"core\",\n    demo: \"ar-card\"\n  },\n  {\n    id: 2,\n    title: \"Multi-Share Methods\",\n    description: \"QR, NFC, camera scan, or just remember a name/number\",\n    icon: \"📱\",\n    category: \"sharing\",\n    demo: \"sharing-methods\"\n  },\n  {\n    id: 3,\n    title: \"Face Recognition\",\n    description: \"Instant card display through facial recognition technology\",\n    icon: \"👤\",\n    category: \"ai\",\n    demo: \"face-recognition\"\n  },\n  {\n    id: 4,\n    title: \"Custom Effects\",\n    description: \"Choose from 50+ preset effects or create your own\",\n    icon: \"✨\",\n    category: \"customization\",\n    demo: \"effects-gallery\"\n  },\n  {\n    id: 5,\n    title: \"No App Required\",\n    description: \"Works directly in any web browser, no downloads needed\",\n    icon: \"🌐\",\n    category: \"accessibility\",\n    demo: \"browser-demo\"\n  },\n  {\n    id: 6,\n    title: \"Analytics Dashboard\",\n    description: \"Track views, interactions, and networking success\",\n    icon: \"📊\",\n    category: \"analytics\",\n    demo: \"analytics\"\n  }\n];\n\nexport const pricingPlans = [\n  {\n    id: \"free\",\n    name: \"Free\",\n    price: 0,\n    period: \"forever\",\n    description: \"Perfect for getting started\",\n    features: [\n      \"1 AR business card\",\n      \"Basic random effects\",\n      \"QR code sharing\",\n      \"Basic analytics\",\n      \"NameCardAI branding\"\n    ],\n    limitations: [\n      \"Limited customization\",\n      \"Basic support only\"\n    ],\n    cta: \"Get Started Free\",\n    popular: false\n  },\n  {\n    id: \"pro\",\n    name: \"Pro\",\n    price: 12,\n    period: \"month\",\n    description: \"For professionals who want to stand out\",\n    features: [\n      \"5 AR business cards\",\n      \"Choose from 10+ effects\",\n      \"All sharing methods\",\n      \"Custom intro templates\",\n      \"Advanced analytics\",\n      \"Remove branding\",\n      \"Priority support\"\n    ],\n    limitations: [],\n    cta: \"Start Pro Trial\",\n    popular: true\n  },\n  {\n    id: \"premium\",\n    name: \"Premium\",\n    price: 25,\n    period: \"month\",\n    description: \"Ultimate networking experience\",\n    features: [\n      \"Unlimited AR cards\",\n      \"Custom AR effects\",\n      \"3D avatar creation\",\n      \"Video intro overlays\",\n      \"Advanced analytics\",\n      \"White-label options\",\n      \"API access\",\n      \"Dedicated support\"\n    ],\n    limitations: [],\n    cta: \"Go Premium\",\n    popular: false\n  }\n];\n\nexport const testimonials = [\n  {\n    id: 1,\n    name: \"Sarah Chen\",\n    role: \"Sales Director\",\n    company: \"TechCorp\",\n    avatar: \"/avatars/sarah.jpg\",\n    content: \"NameCardAI completely transformed how I network. The AR effects are mind-blowing and people remember me instantly!\",\n    rating: 5,\n    featured: true\n  },\n  {\n    id: 2,\n    name: \"Marcus Rodriguez\",\n    role: \"Freelance Designer\",\n    company: \"Independent\",\n    avatar: \"/avatars/marcus.jpg\",\n    content: \"As a freelancer, standing out is crucial. NameCardAI helps me make unforgettable first impressions.\",\n    rating: 5,\n    featured: true\n  },\n  {\n    id: 3,\n    name: \"Dr. Emily Watson\",\n    role: \"Startup Founder\",\n    company: \"MedTech Solutions\",\n    avatar: \"/avatars/emily.jpg\",\n    content: \"The face recognition feature is incredible. Investors remember our pitch because of the innovative card experience.\",\n    rating: 5,\n    featured: true\n  },\n  {\n    id: 4,\n    name: \"James Park\",\n    role: \"Event Organizer\",\n    company: \"Global Events\",\n    avatar: \"/avatars/james.jpg\",\n    content: \"Perfect for conferences! Attendees love the interactive experience and it's eco-friendly too.\",\n    rating: 5,\n    featured: false\n  }\n];\n\nexport const competitors = [\n  {\n    name: \"HiHello\",\n    strengths: [\"Clean UI\", \"Good integrations\"],\n    weaknesses: [\"No AR features\", \"Limited customization\"],\n    pricing: \"$12/month\",\n    marketShare: \"15%\"\n  },\n  {\n    name: \"Mobilo\",\n    strengths: [\"NFC focus\", \"Good analytics\"],\n    weaknesses: [\"Hardware dependency\", \"No 3D/AR\"],\n    pricing: \"$6-15/month\",\n    marketShare: \"10%\"\n  },\n  {\n    name: \"Popl\",\n    strengths: [\"Social integration\", \"Trendy design\"],\n    weaknesses: [\"Limited professional features\", \"No AR\"],\n    pricing: \"$5-10/month\",\n    marketShare: \"8%\"\n  },\n  {\n    name: \"Linq\",\n    strengths: [\"NFC hardware\", \"Enterprise focus\"],\n    weaknesses: [\"High cost\", \"No AR/3D features\"],\n    pricing: \"$15-50/month\",\n    marketShare: \"12%\"\n  }\n];\n\nexport const roadmapItems = [\n  {\n    id: 1,\n    phase: \"MVP\",\n    title: \"Core Platform Launch\",\n    description: \"Web app with QR/Name/Camera scan, 3D card, preset effects\",\n    status: \"in-progress\",\n    quarter: \"Q1 2024\",\n    features: [\n      \"3D card renderer\",\n      \"Basic AR effects\",\n      \"QR code sharing\",\n      \"Name-based search\"\n    ]\n  },\n  {\n    id: 2,\n    phase: \"Phase 1\",\n    title: \"User Dashboard\",\n    description: \"Card editing, intro uploads, effect selection\",\n    status: \"planned\",\n    quarter: \"Q2 2024\",\n    features: [\n      \"Card customization\",\n      \"Video intro upload\",\n      \"Effect library\",\n      \"Analytics dashboard\"\n    ]\n  },\n  {\n    id: 3,\n    phase: \"Phase 2\",\n    title: \"Live AR Mode\",\n    description: \"Real-time camera scan and AR overlays\",\n    status: \"planned\",\n    quarter: \"Q3 2024\",\n    features: [\n      \"Camera integration\",\n      \"Real-time AR\",\n      \"Face matching\",\n      \"Live overlays\"\n    ]\n  },\n  {\n    id: 4,\n    phase: \"Phase 3\",\n    title: \"Mobile PWA\",\n    description: \"Progressive web app with offline capabilities\",\n    status: \"planned\",\n    quarter: \"Q4 2024\",\n    features: [\n      \"PWA installation\",\n      \"Offline mode\",\n      \"Push notifications\",\n      \"Native feel\"\n    ]\n  },\n  {\n    id: 5,\n    phase: \"Phase 4\",\n    title: \"Enterprise Features\",\n    description: \"Company bundles, CRM integration, advanced analytics\",\n    status: \"planned\",\n    quarter: \"Q1 2025\",\n    features: [\n      \"Team management\",\n      \"CRM integration\",\n      \"White-label options\",\n      \"Enterprise security\"\n    ]\n  }\n];\n\nexport const effectTypes = [\n  {\n    id: \"matrix\",\n    name: \"Matrix Effect\",\n    description: \"Falling green characters background\",\n    category: \"background\",\n    preview: \"/effects/matrix.gif\"\n  },\n  {\n    id: \"particles\",\n    name: \"Particle System\",\n    description: \"Floating particles with physics\",\n    category: \"background\",\n    preview: \"/effects/particles.gif\"\n  },\n  {\n    id: \"tilt\",\n    name: \"3D Tilt\",\n    description: \"Interactive 3D perspective on hover\",\n    category: \"interaction\",\n    preview: \"/effects/tilt.gif\"\n  },\n  {\n    id: \"glow\",\n    name: \"Neon Glow\",\n    description: \"Pulsing neon border effects\",\n    category: \"border\",\n    preview: \"/effects/glow.gif\"\n  },\n  {\n    id: \"typing\",\n    name: \"Typing Animation\",\n    description: \"Character-by-character text reveal\",\n    category: \"text\",\n    preview: \"/effects/typing.gif\"\n  },\n  {\n    id: \"smoke\",\n    name: \"Smoke Trail\",\n    description: \"Particle-based smoke effects\",\n    category: \"particle\",\n    preview: \"/effects/smoke.gif\"\n  },\n  {\n    id: \"fireflies\",\n    name: \"Fireflies\",\n    description: \"Floating light particles\",\n    category: \"particle\",\n    preview: \"/effects/fireflies.gif\"\n  },\n  {\n    id: \"hologram\",\n    name: \"Hologram\",\n    description: \"Futuristic holographic display\",\n    category: \"overlay\",\n    preview: \"/effects/hologram.gif\"\n  }\n];\n\nexport const demoCards = [\n  {\n    id: 1,\n    name: \"Alex Thompson\",\n    title: \"Senior Developer\",\n    company: \"TechFlow Inc.\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    website: \"alexthompson.dev\",\n    avatar: \"/avatars/alex.jpg\",\n    background: \"gradient-primary\",\n    effect: \"matrix\",\n    theme: \"dark\"\n  },\n  {\n    id: 2,\n    name: \"Maria Garcia\",\n    title: \"UX Designer\",\n    company: \"Creative Studio\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    website: \"mariagarcia.design\",\n    avatar: \"/avatars/maria.jpg\",\n    background: \"gradient-secondary\",\n    effect: \"particles\",\n    theme: \"light\"\n  },\n  {\n    id: 3,\n    name: \"David Kim\",\n    title: \"Product Manager\",\n    company: \"Innovation Labs\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    website: \"davidkim.pm\",\n    avatar: \"/avatars/david.jpg\",\n    background: \"gradient-accent\",\n    effect: \"hologram\",\n    theme: \"dark\"\n  }\n];\n\nexport const stats = [\n  {\n    label: \"Cards Created\",\n    value: \"50,000+\",\n    icon: \"📇\"\n  },\n  {\n    label: \"Active Users\",\n    value: \"12,500+\",\n    icon: \"👥\"\n  },\n  {\n    label: \"Connections Made\",\n    value: \"250,000+\",\n    icon: \"🤝\"\n  },\n  {\n    label: \"Countries\",\n    value: \"45+\",\n    icon: \"🌍\"\n  }\n];\n\nexport const faqs = [\n  {\n    id: 1,\n    question: \"Do I need to download an app?\",\n    answer: \"No! NameCardAI works directly in any web browser. Recipients can view your AR card instantly without downloading anything.\"\n  },\n  {\n    id: 2,\n    question: \"How does the face recognition work?\",\n    answer: \"Our AI technology can recognize faces and instantly display the associated business card. It's completely privacy-focused and works offline.\"\n  },\n  {\n    id: 3,\n    question: \"Can I customize the AR effects?\",\n    answer: \"Yes! Choose from 50+ preset effects in our Pro plan, or create completely custom AR experiences with our Premium plan.\"\n  },\n  {\n    id: 4,\n    question: \"Is my data secure?\",\n    answer: \"Absolutely. We use enterprise-grade encryption and never share your personal information. You control who sees your card and when.\"\n  },\n  {\n    id: 5,\n    question: \"What devices are supported?\",\n    answer: \"NameCardAI works on any device with a modern web browser - smartphones, tablets, laptops, and desktops.\"\n  }\n];\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,WAAW;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa,EAAE;QACf,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa,EAAE;QACf,KAAK;QACL,SAAS;IACX;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,MAAM,cAAc;IACzB;QACE,MAAM;QACN,WAAW;YAAC;YAAY;SAAoB;QAC5C,YAAY;YAAC;YAAkB;SAAwB;QACvD,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,WAAW;YAAC;YAAa;SAAiB;QAC1C,YAAY;YAAC;YAAuB;SAAW;QAC/C,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,WAAW;YAAC;YAAsB;SAAgB;QAClD,YAAY;YAAC;YAAiC;SAAQ;QACtD,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,WAAW;YAAC;YAAgB;SAAmB;QAC/C,YAAY;YAAC;YAAa;SAAoB;QAC9C,SAAS;QACT,aAAa;IACf;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;CACD;AAEM,MAAM,YAAY;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD;AAEM,MAAM,OAAO;IAClB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 2300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Card.js"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\nconst cardVariants = {\n  default: \"bg-surface border border-surface-light\",\n  glass: \"glass\",\n  gradient: \"bg-gradient-to-br from-surface to-surface-light\",\n  neon: \"bg-surface neon-border\",\n  floating: \"bg-surface border border-surface-light shadow-2xl animate-float\"\n};\n\nexport default function Card({\n  children,\n  className,\n  variant = \"default\",\n  hover = true,\n  glow = false,\n  ...props\n}) {\n  return (\n    <div\n      className={cn(\n        // Base styles\n        \"rounded-xl p-6 transition-all duration-300\",\n        \n        // Hover effects\n        hover && \"hover:scale-105 hover:shadow-2xl cursor-pointer\",\n        \n        // Glow effect\n        glow && \"glow-primary\",\n        \n        // Variants\n        cardVariants[variant],\n        \n        // Custom className\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\nexport function CardHeader({ children, className, ...props }) {\n  return (\n    <div\n      className={cn(\"flex flex-col space-y-1.5 pb-4\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\nexport function CardTitle({ children, className, ...props }) {\n  return (\n    <h3\n      className={cn(\n        \"text-2xl font-semibold leading-none tracking-tight text-gradient\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </h3>\n  );\n}\n\nexport function CardDescription({ children, className, ...props }) {\n  return (\n    <p\n      className={cn(\"text-sm text-text-muted\", className)}\n      {...props}\n    >\n      {children}\n    </p>\n  );\n}\n\nexport function CardContent({ children, className, ...props }) {\n  return (\n    <div className={cn(\"pt-0\", className)} {...props}>\n      {children}\n    </div>\n  );\n}\n\nexport function CardFooter({ children, className, ...props }) {\n  return (\n    <div\n      className={cn(\"flex items-center pt-4\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\n// Specialized card components\nexport function FeatureCard({ icon, title, description, className, ...props }) {\n  return (\n    <Card variant=\"glass\" hover glow className={cn(\"text-center\", className)} {...props}>\n      <CardContent>\n        <div className=\"text-4xl mb-4\">{icon}</div>\n        <CardTitle className=\"mb-2\">{title}</CardTitle>\n        <CardDescription>{description}</CardDescription>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport function PricingCard({ \n  plan, \n  price, \n  period, \n  features, \n  popular = false, \n  className, \n  ...props \n}) {\n  return (\n    <Card \n      variant={popular ? \"neon\" : \"glass\"} \n      hover \n      glow={popular}\n      className={cn(\n        \"relative h-full\",\n        popular && \"scale-105 border-2 border-primary\",\n        className\n      )} \n      {...props}\n    >\n      {popular && (\n        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n          <span className=\"bg-gradient-to-r from-primary to-secondary text-background px-4 py-1 rounded-full text-sm font-medium\">\n            Most Popular\n          </span>\n        </div>\n      )}\n      \n      <CardHeader>\n        <CardTitle>{plan}</CardTitle>\n        <div className=\"text-3xl font-bold text-gradient\">\n          ${price}\n          <span className=\"text-sm text-text-muted font-normal\">/{period}</span>\n        </div>\n      </CardHeader>\n      \n      <CardContent>\n        <ul className=\"space-y-3\">\n          {features.map((feature, index) => (\n            <li key={index} className=\"flex items-center\">\n              <span className=\"text-accent mr-2\">✓</span>\n              <span className=\"text-text-muted\">{feature}</span>\n            </li>\n          ))}\n        </ul>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport function TestimonialCard({ \n  name, \n  role, \n  company, \n  content, \n  avatar, \n  rating = 5,\n  className, \n  ...props \n}) {\n  return (\n    <Card variant=\"glass\" hover className={cn(\"h-full\", className)} {...props}>\n      <CardContent>\n        <div className=\"flex items-center mb-4\">\n          {[...Array(rating)].map((_, i) => (\n            <span key={i} className=\"text-accent text-lg\">★</span>\n          ))}\n        </div>\n        \n        <p className=\"text-text-muted mb-6 italic\">\"{content}\"</p>\n        \n        <div className=\"flex items-center\">\n          <div className=\"w-12 h-12 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-background font-bold mr-4\">\n            {name.charAt(0)}\n          </div>\n          <div>\n            <div className=\"font-semibold text-text\">{name}</div>\n            <div className=\"text-sm text-text-muted\">{role} at {company}</div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,OAAO;IACP,UAAU;IACV,MAAM;IACN,UAAU;AACZ;AAEe,SAAS,KAAK,EAC3B,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,8CAEA,gBAAgB;QAChB,SAAS,mDAET,cAAc;QACd,QAAQ,gBAER,WAAW;QACX,YAAY,CAAC,QAAQ,EAErB,mBAAmB;QACnB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA/BwB;AAiCjB,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC1D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IACzD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;MAZgB;AAcT,SAAS,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC3D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;kBAC7C;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC1D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAO;IAC3E,qBACE,6LAAC;QAAK,SAAQ;QAAQ,KAAK;QAAC,IAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;QAAa,GAAG,KAAK;kBACjF,cAAA,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;8BAAiB;;;;;;8BAChC,6LAAC;oBAAU,WAAU;8BAAQ;;;;;;8BAC7B,6LAAC;8BAAiB;;;;;;;;;;;;;;;;;AAI1B;MAVgB;AAYT,SAAS,YAAY,EAC1B,IAAI,EACJ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,UAAU,KAAK,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,SAAS,UAAU,SAAS;QAC5B,KAAK;QACL,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mBACA,WAAW,qCACX;QAED,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAAwG;;;;;;;;;;;0BAM5H,6LAAC;;kCACC,6LAAC;kCAAW;;;;;;kCACZ,6LAAC;wBAAI,WAAU;;4BAAmC;4BAC9C;0CACF,6LAAC;gCAAK,WAAU;;oCAAsC;oCAAE;;;;;;;;;;;;;;;;;;;0BAI5D,6LAAC;0BACC,cAAA,6LAAC;oBAAG,WAAU;8BACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAAe,WAAU;;8CACxB,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;2BAF5B;;;;;;;;;;;;;;;;;;;;;AASrB;MAjDgB;AAmDT,SAAS,gBAAgB,EAC9B,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,CAAC,EACV,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QAAK,SAAQ;QAAQ,KAAK;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QAAa,GAAG,KAAK;kBACvE,cAAA,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC1B,6LAAC;4BAAa,WAAU;sCAAsB;2BAAnC;;;;;;;;;;8BAIf,6LAAC;oBAAE,WAAU;;wBAA8B;wBAAE;wBAAQ;;;;;;;8BAErD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,CAAC;;;;;;sCAEf,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAA2B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;;wCAA2B;wCAAK;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;MAjCgB", "debugId": null}}, {"offset": {"line": 2664, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/FeaturePreviewSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport { features } from '@/lib/data';\nimport Card, { FeatureCard } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nexport default function FeaturePreviewSection() {\n  const [activeFeature, setActiveFeature] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          // Start auto-rotation when visible\n          intervalRef.current = setInterval(() => {\n            setActiveFeature((prev) => (prev + 1) % features.length);\n          }, 4000);\n        } else {\n          // Stop auto-rotation when not visible\n          if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n          }\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => {\n      observer.disconnect();\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n\n  const handleFeatureClick = (index) => {\n    setActiveFeature(index);\n    // Reset auto-rotation timer\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = setInterval(() => {\n        setActiveFeature((prev) => (prev + 1) % features.length);\n      }, 4000);\n    }\n  };\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-surface relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10\" style={{\n          backgroundSize: '100px 100px'\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">Powerful Features</span>\n            <br />\n            <span className=\"text-text\">Built for the Future</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto\">\n            Discover the cutting-edge capabilities that make NameCardAI the most advanced \n            digital business card platform in the world.\n          </p>\n        </div>\n\n        {/* Feature Showcase */}\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-16\">\n          {/* Feature List */}\n          <div className=\"space-y-4\">\n            {features.map((feature, index) => (\n              <div\n                key={feature.id}\n                className={cn(\n                  \"p-6 rounded-xl cursor-pointer transition-all duration-500 border\",\n                  activeFeature === index\n                    ? \"glass neon-border glow-primary scale-105\"\n                    : \"bg-surface-light border-surface-light hover:border-primary/50\",\n                  isVisible \n                    ? \"opacity-100 translate-x-0\" \n                    : \"opacity-0 -translate-x-10\"\n                )}\n                style={{ transitionDelay: `${index * 100}ms` }}\n                onClick={() => handleFeatureClick(index)}\n              >\n                <div className=\"flex items-center space-x-4\">\n                  <div className={cn(\n                    \"text-3xl p-3 rounded-lg transition-all duration-300\",\n                    activeFeature === index\n                      ? \"bg-gradient-to-r from-primary to-secondary\"\n                      : \"bg-surface\"\n                  )}>\n                    {feature.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className={cn(\n                      \"text-xl font-semibold mb-2 transition-colors duration-300\",\n                      activeFeature === index ? \"text-gradient\" : \"text-text\"\n                    )}>\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-text-muted\">\n                      {feature.description}\n                    </p>\n                  </div>\n                  <div className={cn(\n                    \"w-3 h-3 rounded-full transition-all duration-300\",\n                    activeFeature === index \n                      ? \"bg-primary animate-pulse\" \n                      : \"bg-surface-light\"\n                  )} />\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Feature Demo */}\n          <div className=\"relative\">\n            <div className={cn(\n              \"transition-all duration-700\",\n              isVisible ? \"opacity-100 scale-100\" : \"opacity-0 scale-95\"\n            )}>\n              {/* Main Demo Container */}\n              <div className=\"glass p-8 rounded-2xl neon-border glow-primary relative overflow-hidden\">\n                {/* Demo Content Based on Active Feature */}\n                <div className=\"text-center space-y-6\">\n                  <div className=\"text-6xl mb-4 animate-float\">\n                    {features[activeFeature]?.icon}\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gradient\">\n                    {features[activeFeature]?.title}\n                  </h3>\n                  \n                  <p className=\"text-text-muted\">\n                    {features[activeFeature]?.description}\n                  </p>\n\n                  {/* Interactive Demo Elements */}\n                  <div className=\"mt-8\">\n                    {activeFeature === 0 && (\n                      // AR Card Demo\n                      <div className=\"relative\">\n                        <div className=\"w-64 h-40 mx-auto bg-gradient-to-r from-primary/20 to-secondary/20 rounded-xl border border-primary/50 flex items-center justify-center animate-pulse-glow\">\n                          <div className=\"text-center\">\n                            <div className=\"w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-2\"></div>\n                            <div className=\"text-sm text-text\">AR Card Preview</div>\n                          </div>\n                        </div>\n                        <div className=\"absolute -top-2 -right-2 w-6 h-6 bg-accent rounded-full animate-pulse\"></div>\n                        <div className=\"absolute -bottom-2 -left-2 w-4 h-4 bg-secondary rounded-full animate-float\"></div>\n                      </div>\n                    )}\n\n                    {activeFeature === 1 && (\n                      // Sharing Methods Demo\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        {['QR', 'NFC', '📷', '👤'].map((method, i) => (\n                          <div key={i} className=\"p-4 bg-surface rounded-lg border border-primary/30 text-center animate-pulse-glow\" style={{ animationDelay: `${i * 200}ms` }}>\n                            <div className=\"text-2xl mb-1\">{method}</div>\n                            <div className=\"text-xs text-text-muted\">Share Method</div>\n                          </div>\n                        ))}\n                      </div>\n                    )}\n\n                    {activeFeature === 2 && (\n                      // Face Recognition Demo\n                      <div className=\"relative\">\n                        <div className=\"w-32 h-32 mx-auto bg-gradient-to-r from-accent/20 to-primary/20 rounded-full border-2 border-accent flex items-center justify-center\">\n                          <span className=\"text-3xl\">👤</span>\n                        </div>\n                        <div className=\"absolute inset-0 border-2 border-accent rounded-full animate-ping\"></div>\n                        <div className=\"mt-4 text-sm text-accent\">Face Recognition Active</div>\n                      </div>\n                    )}\n\n                    {activeFeature === 3 && (\n                      // Effects Gallery Demo\n                      <div className=\"grid grid-cols-3 gap-2\">\n                        {['✨', '🌟', '💫', '🎆', '🔥', '⚡'].map((effect, i) => (\n                          <div key={i} className=\"p-3 bg-surface rounded-lg border border-secondary/30 text-center hover:scale-110 transition-transform cursor-pointer\">\n                            <div className=\"text-xl\">{effect}</div>\n                          </div>\n                        ))}\n                      </div>\n                    )}\n\n                    {activeFeature === 4 && (\n                      // Browser Demo\n                      <div className=\"space-y-3\">\n                        <div className=\"flex justify-center space-x-4\">\n                          {['🌐', '📱', '💻', '⌚'].map((device, i) => (\n                            <div key={i} className=\"w-12 h-12 bg-surface rounded-lg border border-primary/30 flex items-center justify-center animate-bounce\" style={{ animationDelay: `${i * 100}ms` }}>\n                              <span className=\"text-lg\">{device}</span>\n                            </div>\n                          ))}\n                        </div>\n                        <div className=\"text-sm text-primary\">Works on all devices</div>\n                      </div>\n                    )}\n\n                    {activeFeature === 5 && (\n                      // Analytics Demo\n                      <div className=\"space-y-4\">\n                        <div className=\"grid grid-cols-3 gap-4 text-center\">\n                          <div>\n                            <div className=\"text-2xl font-bold text-gradient\">1.2K</div>\n                            <div className=\"text-xs text-text-muted\">Views</div>\n                          </div>\n                          <div>\n                            <div className=\"text-2xl font-bold text-gradient\">89%</div>\n                            <div className=\"text-xs text-text-muted\">Engagement</div>\n                          </div>\n                          <div>\n                            <div className=\"text-2xl font-bold text-gradient\">156</div>\n                            <div className=\"text-xs text-text-muted\">Contacts</div>\n                          </div>\n                        </div>\n                        <div className=\"h-2 bg-surface rounded-full overflow-hidden\">\n                          <div className=\"h-full bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse\" style={{ width: '89%' }}></div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  <Button variant=\"outline\" size=\"sm\">\n                    Try {features[activeFeature]?.title}\n                  </Button>\n                </div>\n\n                {/* Progress Indicator */}\n                <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2\">\n                  {features.map((_, index) => (\n                    <div\n                      key={index}\n                      className={cn(\n                        \"w-2 h-2 rounded-full transition-all duration-300 cursor-pointer\",\n                        activeFeature === index \n                          ? \"bg-primary w-8\" \n                          : \"bg-surface-light hover:bg-primary/50\"\n                      )}\n                      onClick={() => handleFeatureClick(index)}\n                    />\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Feature Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <FeatureCard\n              key={feature.id}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              className={cn(\n                \"transition-all duration-700\",\n                isVisible \n                  ? \"opacity-100 translate-y-0\" \n                  : \"opacity-0 translate-y-10\"\n              )}\n              style={{ transitionDelay: `${index * 150}ms` }}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,WAAW,IAAI;mDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,mCAAmC;wBACnC,YAAY,OAAO,GAAG;+DAAY;gCAChC;uEAAiB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,qHAAA,CAAA,WAAQ,CAAC,MAAM;;4BACzD;8DAAG;oBACL,OAAO;wBACL,sCAAsC;wBACtC,IAAI,YAAY,OAAO,EAAE;4BACvB,cAAc,YAAY,OAAO;wBACnC;oBACF;gBACF;kDACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;mDAAO;oBACL,SAAS,UAAU;oBACnB,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;gBACF;;QACF;0CAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;QACjB,4BAA4B;QAC5B,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG,YAAY;gBAChC,iBAAiB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,qHAAA,CAAA,WAAQ,CAAC,MAAM;YACzD,GAAG;QACL;IACF;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAqF,OAAO;wBACzG,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAO3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,qHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,kBAAkB,QACd,6CACA,iEACJ,YACI,8BACA;wCAEN,OAAO;4CAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;wCAAC;wCAC7C,SAAS,IAAM,mBAAmB;kDAElC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uDACA,kBAAkB,QACd,+CACA;8DAEH,QAAQ,IAAI;;;;;;8DAEf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,6DACA,kBAAkB,QAAQ,kBAAkB;sEAE3C,QAAQ,KAAK;;;;;;sEAEhB,6LAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;8DAGxB,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oDACA,kBAAkB,QACd,6BACA;;;;;;;;;;;;uCArCH,QAAQ,EAAE;;;;;;;;;;0CA6CrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,YAAY,0BAA0B;8CAGtC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,qHAAA,CAAA,WAAQ,CAAC,cAAc,EAAE;;;;;;kEAG5B,6LAAC;wDAAG,WAAU;kEACX,qHAAA,CAAA,WAAQ,CAAC,cAAc,EAAE;;;;;;kEAG5B,6LAAC;wDAAE,WAAU;kEACV,qHAAA,CAAA,WAAQ,CAAC,cAAc,EAAE;;;;;;kEAI5B,6LAAC;wDAAI,WAAU;;4DACZ,kBAAkB,KACjB,eAAe;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAI,WAAU;8FAAoB;;;;;;;;;;;;;;;;;kFAGvC,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;4DAIlB,kBAAkB,KACjB,uBAAuB;0EACvB,6LAAC;gEAAI,WAAU;0EACZ;oEAAC;oEAAM;oEAAO;oEAAM;iEAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;wEAAY,WAAU;wEAAoF,OAAO;4EAAE,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;wEAAC;;0FACjJ,6LAAC;gFAAI,WAAU;0FAAiB;;;;;;0FAChC,6LAAC;gFAAI,WAAU;0FAA0B;;;;;;;uEAFjC;;;;;;;;;;4DAQf,kBAAkB,KACjB,wBAAwB;0EACxB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;kFAE7B,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;kFAA2B;;;;;;;;;;;;4DAI7C,kBAAkB,KACjB,uBAAuB;0EACvB,6LAAC;gEAAI,WAAU;0EACZ;oEAAC;oEAAK;oEAAM;oEAAM;oEAAM;oEAAM;iEAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,kBAC/C,6LAAC;wEAAY,WAAU;kFACrB,cAAA,6LAAC;4EAAI,WAAU;sFAAW;;;;;;uEADlB;;;;;;;;;;4DAOf,kBAAkB,KACjB,eAAe;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ;4EAAC;4EAAM;4EAAM;4EAAM;yEAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACpC,6LAAC;gFAAY,WAAU;gFAA2G,OAAO;oFAAE,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;gFAAC;0FACxK,cAAA,6LAAC;oFAAK,WAAU;8FAAW;;;;;;+EADnB;;;;;;;;;;kFAKd,6LAAC;wEAAI,WAAU;kFAAuB;;;;;;;;;;;;4DAIzC,kBAAkB,KACjB,iBAAiB;0EACjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAI,WAAU;kGAAmC;;;;;;kGAClD,6LAAC;wFAAI,WAAU;kGAA0B;;;;;;;;;;;;0FAE3C,6LAAC;;kGACC,6LAAC;wFAAI,WAAU;kGAAmC;;;;;;kGAClD,6LAAC;wFAAI,WAAU;kGAA0B;;;;;;;;;;;;0FAE3C,6LAAC;;kGACC,6LAAC;wFAAI,WAAU;kGAAmC;;;;;;kGAClD,6LAAC;wFAAI,WAAU;kGAA0B;;;;;;;;;;;;;;;;;;kFAG7C,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA+E,OAAO;gFAAE,OAAO;4EAAM;;;;;;;;;;;;;;;;;;;;;;;kEAM5H,6LAAC,oIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;;4DAAK;4DAC7B,qHAAA,CAAA,WAAQ,CAAC,cAAc,EAAE;;;;;;;;;;;;;0DAKlC,6LAAC;gDAAI,WAAU;0DACZ,qHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,sBAChB,6LAAC;wDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mEACA,kBAAkB,QACd,mBACA;wDAEN,SAAS,IAAM,mBAAmB;uDAP7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBnB,6LAAC;wBAAI,WAAU;kCACZ,qHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,kIAAA,CAAA,cAAW;gCAEV,MAAM,QAAQ,IAAI;gCAClB,OAAO,QAAQ,KAAK;gCACpB,aAAa,QAAQ,WAAW;gCAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YACI,8BACA;gCAEN,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;+BAVxC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAiB7B;GArRwB;KAAA", "debugId": null}}, {"offset": {"line": 3341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/PricingSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport { pricingPlans } from '@/lib/data';\nimport { PricingCard } from '@/components/ui/Card';\nimport Button, { GradientButton } from '@/components/ui/Button';\n\nexport default function PricingSection() {\n  const [isVisible, setIsVisible] = useState(false);\n  const [billingCycle, setBillingCycle] = useState('monthly');\n  const sectionRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const getPrice = (plan) => {\n    if (plan.id === 'free') return 0;\n    return billingCycle === 'yearly' ? Math.floor(plan.price * 10) : plan.price;\n  };\n\n  const getPeriod = () => {\n    return billingCycle === 'yearly' ? 'year' : 'month';\n  };\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gradient-to-b from-surface to-background relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-20\">\n        {/* Animated grid pattern */}\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px), \n                           linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px)`,\n          backgroundSize: '50px 50px',\n          animation: 'matrix 20s linear infinite'\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">Simple Pricing</span>\n            <br />\n            <span className=\"text-text\">Powerful Results</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto mb-8\">\n            Choose the perfect plan for your networking needs. Start free and upgrade \n            as you grow your professional connections.\n          </p>\n\n          {/* Billing Toggle */}\n          <div className=\"flex items-center justify-center space-x-4 mb-12\">\n            <span className={cn(\n              \"text-lg font-medium transition-colors\",\n              billingCycle === 'monthly' ? \"text-primary\" : \"text-text-muted\"\n            )}>\n              Monthly\n            </span>\n            <button\n              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}\n              className={cn(\n                \"relative w-16 h-8 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\",\n                billingCycle === 'yearly' ? \"bg-primary\" : \"bg-surface-light\"\n              )}\n            >\n              <div className={cn(\n                \"absolute top-1 w-6 h-6 bg-white rounded-full transition-transform duration-300\",\n                billingCycle === 'yearly' ? \"translate-x-8\" : \"translate-x-1\"\n              )} />\n            </button>\n            <span className={cn(\n              \"text-lg font-medium transition-colors\",\n              billingCycle === 'yearly' ? \"text-primary\" : \"text-text-muted\"\n            )}>\n              Yearly\n            </span>\n            {billingCycle === 'yearly' && (\n              <span className=\"bg-gradient-to-r from-primary to-secondary text-background px-3 py-1 rounded-full text-sm font-medium animate-pulse-glow\">\n                Save 20%\n              </span>\n            )}\n          </div>\n        </div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {pricingPlans.map((plan, index) => (\n            <div\n              key={plan.id}\n              className={cn(\n                \"transition-all duration-700\",\n                isVisible \n                  ? \"opacity-100 translate-y-0\" \n                  : \"opacity-0 translate-y-10\",\n                plan.popular && \"md:-mt-4\"\n              )}\n              style={{ transitionDelay: `${index * 200}ms` }}\n            >\n              <div className={cn(\n                \"relative h-full transition-all duration-300\",\n                plan.popular \n                  ? \"glass neon-border glow-primary scale-105\" \n                  : \"glass hover:scale-105\"\n              )}>\n                {/* Popular Badge */}\n                {plan.popular && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                    <span className=\"bg-gradient-to-r from-primary to-secondary text-background px-6 py-2 rounded-full text-sm font-bold animate-pulse-glow\">\n                      🔥 Most Popular\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"p-8\">\n                  {/* Plan Header */}\n                  <div className=\"text-center mb-8\">\n                    <h3 className=\"text-2xl font-bold text-gradient mb-2\">\n                      {plan.name}\n                    </h3>\n                    <p className=\"text-text-muted mb-6\">\n                      {plan.description}\n                    </p>\n                    \n                    {/* Price */}\n                    <div className=\"mb-6\">\n                      <div className=\"flex items-baseline justify-center\">\n                        <span className=\"text-5xl font-bold text-gradient\">\n                          ${getPrice(plan)}\n                        </span>\n                        <span className=\"text-text-muted ml-2\">\n                          /{getPeriod()}\n                        </span>\n                      </div>\n                      {billingCycle === 'yearly' && plan.id !== 'free' && (\n                        <div className=\"text-sm text-text-muted mt-2\">\n                          <span className=\"line-through\">${plan.price * 12}</span>\n                          <span className=\"text-primary ml-2\">Save ${plan.price * 12 - getPrice(plan)}</span>\n                        </div>\n                      )}\n                    </div>\n\n                    {/* CTA Button */}\n                    {plan.popular ? (\n                      <GradientButton className=\"w-full mb-6\">\n                        {plan.cta}\n                      </GradientButton>\n                    ) : (\n                      <Button \n                        variant={plan.id === 'free' ? 'outline' : 'default'} \n                        className=\"w-full mb-6\"\n                      >\n                        {plan.cta}\n                      </Button>\n                    )}\n                  </div>\n\n                  {/* Features List */}\n                  <div className=\"space-y-4\">\n                    <h4 className=\"font-semibold text-text mb-3\">What's included:</h4>\n                    <ul className=\"space-y-3\">\n                      {plan.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-start\">\n                          <span className=\"text-accent mr-3 mt-0.5 flex-shrink-0\">✓</span>\n                          <span className=\"text-text-muted text-sm\">{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n\n                    {/* Limitations */}\n                    {plan.limitations && plan.limitations.length > 0 && (\n                      <div className=\"pt-4 border-t border-surface-light\">\n                        <ul className=\"space-y-2\">\n                          {plan.limitations.map((limitation, limitIndex) => (\n                            <li key={limitIndex} className=\"flex items-start\">\n                              <span className=\"text-red-400 mr-3 mt-0.5 flex-shrink-0\">×</span>\n                              <span className=\"text-text-dim text-sm\">{limitation}</span>\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"glass p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-6\">\n              All Plans Include\n            </h3>\n            \n            <div className=\"grid md:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-2\">🔒</div>\n                <div className=\"font-semibold text-text mb-1\">Secure</div>\n                <div className=\"text-sm text-text-muted\">Enterprise-grade security</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-2\">🌐</div>\n                <div className=\"font-semibold text-text mb-1\">Global</div>\n                <div className=\"text-sm text-text-muted\">Works worldwide</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-2\">📱</div>\n                <div className=\"font-semibold text-text mb-1\">Mobile</div>\n                <div className=\"text-sm text-text-muted\">All devices supported</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-2\">💬</div>\n                <div className=\"font-semibold text-text mb-1\">Support</div>\n                <div className=\"text-sm text-text-muted\">24/7 assistance</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* FAQ Preview */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"text-2xl font-bold text-text mb-4\">\n            Questions? We've got answers.\n          </h3>\n          <p className=\"text-text-muted mb-8\">\n            Check out our comprehensive FAQ or contact our support team.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button variant=\"outline\">\n              <span className=\"mr-2\">❓</span>\n              View FAQ\n            </Button>\n            <Button variant=\"ghost\">\n              <span className=\"mr-2\">💬</span>\n              Contact Support\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW,IAAI;4CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;2CACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;4CAAO,IAAM,SAAS,UAAU;;QAClC;mCAAG,EAAE;IAEL,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK,EAAE,KAAK,QAAQ,OAAO;QAC/B,OAAO,iBAAiB,WAAW,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK;IAC7E;IAEA,MAAM,YAAY;QAChB,OAAO,iBAAiB,WAAW,SAAS;IAC9C;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;8FACkE,CAAC;wBACrF,gBAAgB;wBAChB,WAAW;oBACb;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAM9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,yCACA,iBAAiB,YAAY,iBAAiB;kDAC7C;;;;;;kDAGH,6LAAC;wCACC,SAAS,IAAM,gBAAgB,iBAAiB,YAAY,WAAW;wCACvE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA,iBAAiB,WAAW,eAAe;kDAG7C,cAAA,6LAAC;4CAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kFACA,iBAAiB,WAAW,kBAAkB;;;;;;;;;;;kDAGlD,6LAAC;wCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,yCACA,iBAAiB,WAAW,iBAAiB;kDAC5C;;;;;;oCAGF,iBAAiB,0BAChB,6LAAC;wCAAK,WAAU;kDAA2H;;;;;;;;;;;;;;;;;;kCAQjJ,6LAAC;wBAAI,WAAU;kCACZ,qHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YACI,8BACA,4BACJ,KAAK,OAAO,IAAI;gCAElB,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE7C,cAAA,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+CACA,KAAK,OAAO,GACR,6CACA;;wCAGH,KAAK,OAAO,kBACX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAyH;;;;;;;;;;;sDAM7I,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAInB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;gFAAmC;gFAC/C,SAAS;;;;;;;sFAEb,6LAAC;4EAAK,WAAU;;gFAAuB;gFACnC;;;;;;;;;;;;;gEAGL,iBAAiB,YAAY,KAAK,EAAE,KAAK,wBACxC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;gFAAe;gFAAE,KAAK,KAAK,GAAG;;;;;;;sFAC9C,6LAAC;4EAAK,WAAU;;gFAAoB;gFAAO,KAAK,KAAK,GAAG,KAAK,SAAS;;;;;;;;;;;;;;;;;;;wDAM3E,KAAK,OAAO,iBACX,6LAAC,oIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,KAAK,GAAG;;;;;iFAGX,6LAAC,oIAAA,CAAA,UAAM;4DACL,SAAS,KAAK,EAAE,KAAK,SAAS,YAAY;4DAC1C,WAAU;sEAET,KAAK,GAAG;;;;;;;;;;;;8DAMf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DAAG,WAAU;sEACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;oEAAsB,WAAU;;sFAC/B,6LAAC;4EAAK,WAAU;sFAAwC;;;;;;sFACxD,6LAAC;4EAAK,WAAU;sFAA2B;;;;;;;mEAFpC;;;;;;;;;;wDAQZ,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,mBAC7C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAG,WAAU;0EACX,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,2BACjC,6LAAC;wEAAoB,WAAU;;0FAC7B,6LAAC;gFAAK,WAAU;0FAAyC;;;;;;0FACzD,6LAAC;gFAAK,WAAU;0FAAyB;;;;;;;uEAFlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BArFlB,KAAK,EAAE;;;;;;;;;;kCAqGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;8DAA+B;;;;;;8DAC9C,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;8DAA+B;;;;;;8DAC9C,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;8DAA+B;;;;;;8DAC9C,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;8DAA+B;;;;;;8DAC9C,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6LAAC;gCAAE,WAAU;0CAAuB;;;;;;0CAIpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;;0DACd,6LAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAQ;;;;;;;kDAGjC,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;;0DACd,6LAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GA7PwB;KAAA", "debugId": null}}, {"offset": {"line": 4022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/TestimonialsSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport { testimonials, stats } from '@/lib/data';\nimport { TestimonialCard } from '@/components/ui/Card';\n\nexport default function TestimonialsSection() {\n  const [isVisible, setIsVisible] = useState(false);\n  const [activeTestimonial, setActiveTestimonial] = useState(0);\n  const sectionRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          // Start auto-rotation\n          intervalRef.current = setInterval(() => {\n            setActiveTestimonial((prev) => (prev + 1) % testimonials.filter(t => t.featured).length);\n          }, 5000);\n        } else {\n          if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n          }\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => {\n      observer.disconnect();\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n\n  const featuredTestimonials = testimonials.filter(t => t.featured);\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-background relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-20\">\n        {/* Floating testimonial bubbles */}\n        {[...Array(10)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute w-4 h-4 bg-primary/30 rounded-full animate-float\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${4 + Math.random() * 2}s`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">Loved by Professionals</span>\n            <br />\n            <span className=\"text-text\">Worldwide</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto\">\n            Join thousands of professionals who've revolutionized their networking \n            with NameCardAI's AR-enhanced digital business cards.\n          </p>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\">\n          {stats.map((stat, index) => (\n            <div\n              key={index}\n              className={cn(\n                \"text-center transition-all duration-700\",\n                isVisible \n                  ? \"opacity-100 translate-y-0\" \n                  : \"opacity-0 translate-y-10\"\n              )}\n              style={{ transitionDelay: `${index * 150}ms` }}\n            >\n              <div className=\"glass p-6 rounded-xl hover:scale-105 transition-transform duration-300\">\n                <div className=\"text-4xl mb-2\">{stat.icon}</div>\n                <div className=\"text-3xl font-bold text-gradient mb-1\">\n                  {stat.value}\n                </div>\n                <div className=\"text-text-muted text-sm\">\n                  {stat.label}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Featured Testimonial Carousel */}\n        <div className=\"mb-16\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"relative\">\n              {/* Main Testimonial Display */}\n              <div className=\"glass p-8 md:p-12 rounded-2xl neon-border glow-primary text-center\">\n                <div className={cn(\n                  \"transition-all duration-500\",\n                  isVisible ? \"opacity-100 scale-100\" : \"opacity-0 scale-95\"\n                )}>\n                  {/* Quote Icon */}\n                  <div className=\"text-6xl text-primary/30 mb-6\">\"</div>\n                  \n                  {/* Testimonial Content */}\n                  <blockquote className=\"text-xl md:text-2xl text-text mb-8 italic leading-relaxed\">\n                    {featuredTestimonials[activeTestimonial]?.content}\n                  </blockquote>\n                  \n                  {/* Rating */}\n                  <div className=\"flex justify-center mb-6\">\n                    {[...Array(5)].map((_, i) => (\n                      <span key={i} className=\"text-2xl text-accent\">★</span>\n                    ))}\n                  </div>\n                  \n                  {/* Author Info */}\n                  <div className=\"flex items-center justify-center space-x-4\">\n                    <div className=\"w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-background font-bold text-xl\">\n                      {featuredTestimonials[activeTestimonial]?.name.charAt(0)}\n                    </div>\n                    <div className=\"text-left\">\n                      <div className=\"font-semibold text-text text-lg\">\n                        {featuredTestimonials[activeTestimonial]?.name}\n                      </div>\n                      <div className=\"text-text-muted\">\n                        {featuredTestimonials[activeTestimonial]?.role} at {featuredTestimonials[activeTestimonial]?.company}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Navigation Dots */}\n              <div className=\"flex justify-center mt-8 space-x-3\">\n                {featuredTestimonials.map((_, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setActiveTestimonial(index)}\n                    className={cn(\n                      \"w-3 h-3 rounded-full transition-all duration-300\",\n                      activeTestimonial === index \n                        ? \"bg-primary w-8\" \n                        : \"bg-surface-light hover:bg-primary/50\"\n                    )}\n                  />\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* All Testimonials Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <TestimonialCard\n              key={testimonial.id}\n              name={testimonial.name}\n              role={testimonial.role}\n              company={testimonial.company}\n              content={testimonial.content}\n              avatar={testimonial.avatar}\n              rating={testimonial.rating}\n              className={cn(\n                \"transition-all duration-700\",\n                isVisible \n                  ? \"opacity-100 translate-y-0\" \n                  : \"opacity-0 translate-y-10\"\n              )}\n              style={{ transitionDelay: `${index * 150}ms` }}\n            />\n          ))}\n        </div>\n\n        {/* Social Proof Section */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"glass p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-6\">\n              Trusted by Industry Leaders\n            </h3>\n            \n            {/* Company Logos Placeholder */}\n            <div className=\"grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60\">\n              {['TechCorp', 'InnovateLab', 'FutureWorks', 'DigitalFirst', 'NextGen'].map((company, index) => (\n                <div\n                  key={index}\n                  className=\"text-center p-4 bg-surface rounded-lg border border-surface-light hover:border-primary/50 transition-colors\"\n                >\n                  <div className=\"text-2xl mb-2\">🏢</div>\n                  <div className=\"text-sm text-text-muted font-medium\">{company}</div>\n                </div>\n              ))}\n            </div>\n            \n            <div className=\"mt-8 text-text-muted\">\n              Join 12,500+ professionals from leading companies worldwide\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-text mb-4\">\n              Ready to join them?\n            </h3>\n            <p className=\"text-text-muted mb-8\">\n              Start creating your AR-enhanced digital business card today and \n              experience the future of professional networking.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95 bg-gradient-to-r from-primary to-secondary text-background hover:from-primary/90 hover:to-secondary/90 h-12 px-8 text-lg\">\n                <span className=\"mr-2\">🚀</span>\n                Start Free Trial\n              </button>\n              <button className=\"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95 border border-primary text-primary hover:bg-primary hover:text-background neon-border h-12 px-8 text-lg\">\n                <span className=\"mr-2\">👥</span>\n                Join Community\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,WAAW,IAAI;iDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,sBAAsB;wBACtB,YAAY,OAAO,GAAG;6DAAY;gCAChC;qEAAqB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,qHAAA,CAAA,eAAY,CAAC,MAAM;6EAAC,CAAA,IAAK,EAAE,QAAQ;4EAAE,MAAM;;4BACzF;4DAAG;oBACL,OAAO;wBACL,IAAI,YAAY,OAAO,EAAE;4BACvB,cAAc,YAAY,OAAO;wBACnC;oBACF;gBACF;gDACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;iDAAO;oBACL,SAAS,UAAU;oBACnB,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;gBACF;;QACF;wCAAG,EAAE;IAEL,MAAM,uBAAuB,qHAAA,CAAA,eAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ;IAEhE,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BAEZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wBAChD;uBAPK;;;;;;;;;;0BAYX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAO3D,6LAAC;wBAAI,WAAU;kCACZ,qHAAA,CAAA,QAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA,YACI,8BACA;gCAEN,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE7C,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAiB,KAAK,IAAI;;;;;;sDACzC,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;;;;;;+BAfV;;;;;;;;;;kCAuBX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,YAAY,0BAA0B;;8DAGtC,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAG/C,6LAAC;oDAAW,WAAU;8DACnB,oBAAoB,CAAC,kBAAkB,EAAE;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4DAAa,WAAU;sEAAuB;2DAApC;;;;;;;;;;8DAKf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,oBAAoB,CAAC,kBAAkB,EAAE,KAAK,OAAO;;;;;;sEAExD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,oBAAoB,CAAC,kBAAkB,EAAE;;;;;;8EAE5C,6LAAC;oEAAI,WAAU;;wEACZ,oBAAoB,CAAC,kBAAkB,EAAE;wEAAK;wEAAK,oBAAoB,CAAC,kBAAkB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQvG,6LAAC;wCAAI,WAAU;kDACZ,qBAAqB,GAAG,CAAC,CAAC,GAAG,sBAC5B,6LAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA,sBAAsB,QAClB,mBACA;+CAND;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBjB,6LAAC;wBAAI,WAAU;kCACZ,qHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,kIAAA,CAAA,kBAAe;gCAEd,MAAM,YAAY,IAAI;gCACtB,MAAM,YAAY,IAAI;gCACtB,SAAS,YAAY,OAAO;gCAC5B,SAAS,YAAY,OAAO;gCAC5B,QAAQ,YAAY,MAAM;gCAC1B,QAAQ,YAAY,MAAM;gCAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YACI,8BACA;gCAEN,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;+BAbxC,YAAY,EAAE;;;;;;;;;;kCAmBzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAY;wCAAe;wCAAe;wCAAgB;qCAAU,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnF,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;8DAAuC;;;;;;;2CAJjD;;;;;;;;;;8CASX,6LAAC;oCAAI,WAAU;8CAAuB;;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,6LAAC;oCAAE,WAAU;8CAAuB;;;;;;8CAKpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GAxOwB;KAAA", "debugId": null}}, {"offset": {"line": 4537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Why Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Community', href: '/community' },\n      { name: 'Templates', href: '/templates' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', href: '#', icon: '🐦' },\n    { name: 'LinkedIn', href: '#', icon: '💼' },\n    { name: 'GitHub', href: '#', icon: '🐙' },\n    { name: 'Discord', href: '#', icon: '💬' },\n    { name: 'YouTube', href: '#', icon: '📺' }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-surface-light relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `radial-gradient(circle at 20% 80%, var(--primary) 0%, transparent 50%), \n                           radial-gradient(circle at 80% 20%, var(--secondary) 0%, transparent 50%)`,\n          backgroundSize: '200px 200px'\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-5 gap-12\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              {/* Logo */}\n              <Link href=\"/\" className=\"flex items-center space-x-3 group mb-6\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300\">\n                  <svg\n                    className=\"w-8 h-8 text-background\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n                  </svg>\n                </div>\n                <span className=\"text-2xl font-bold font-display text-gradient\">\n                  NameCardAI\n                </span>\n              </Link>\n\n              {/* Description */}\n              <p className=\"text-text-muted mb-6 leading-relaxed\">\n                Revolutionizing professional networking with AR-enhanced digital business cards. \n                Share stunning, interactive profiles that work without apps and create lasting impressions.\n              </p>\n\n              {/* Newsletter Signup */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-text\">Stay Updated</h4>\n                <div className=\"flex space-x-3\">\n                  <input\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"flex-1 px-4 py-2 bg-background border border-surface-light rounded-lg text-text placeholder-text-dim focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                  />\n                  <Button variant=\"gradient\" size=\"sm\">\n                    Subscribe\n                  </Button>\n                </div>\n                <p className=\"text-xs text-text-dim\">\n                  Get the latest updates on new features and releases.\n                </p>\n              </div>\n            </div>\n\n            {/* Links Sections */}\n            <div className=\"lg:col-span-3 grid sm:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {/* Product */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Product</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.product.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Company */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Company</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.company.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Resources */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Resources</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.resources.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Legal */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Legal</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.legal.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"py-8 border-t border-surface-light\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-text-muted text-sm\">\n              © {currentYear} NameCardAI. All rights reserved. Built with ❤️ for the future of networking.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-6\">\n              <span className=\"text-text-muted text-sm\">Follow us:</span>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social) => (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"text-text-muted hover:text-primary transition-colors duration-300 hover:scale-110 transform\"\n                    title={social.name}\n                  >\n                    <span className=\"text-xl\">{social.icon}</span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"py-6 border-t border-surface-light\">\n          <div className=\"grid md:grid-cols-3 gap-6 text-center md:text-left\">\n            <div>\n              <h5 className=\"font-semibold text-text mb-2\">🌍 Global Reach</h5>\n              <p className=\"text-text-muted text-sm\">\n                Available in 45+ countries with 24/7 support\n              </p>\n            </div>\n            \n            <div>\n              <h5 className=\"font-semibold text-text mb-2\">🔒 Enterprise Security</h5>\n              <p className=\"text-text-muted text-sm\">\n                SOC 2 compliant with end-to-end encryption\n              </p>\n            </div>\n            \n            <div>\n              <h5 className=\"font-semibold text-text mb-2\">⚡ 99.9% Uptime</h5>\n              <p className=\"text-text-muted text-sm\">\n                Reliable infrastructure with global CDN\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Action Button */}\n      <div className=\"fixed bottom-8 right-8 z-50\">\n        <Button\n          variant=\"gradient\"\n          size=\"lg\"\n          className=\"rounded-full w-16 h-16 shadow-2xl animate-pulse-glow\"\n          title=\"Get Help\"\n        >\n          <span className=\"text-2xl\">💬</span>\n        </Button>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;SACzC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAK;QACzC;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM;QAAK;QAC1C;YAAE,MAAM;YAAU,MAAM;YAAK,MAAM;QAAK;QACxC;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAK;QACzC;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAK;KAC1C;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;mGACuE,CAAC;wBAC1F,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,MAAK;wDACL,SAAQ;kEAER,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAK,WAAU;8DAAgD;;;;;;;;;;;;sDAMlE,6LAAC;4CAAE,WAAU;sDAAuC;;;;;;sDAMpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC,oIAAA,CAAA,UAAM;4DAAC,SAAQ;4DAAW,MAAK;sEAAK;;;;;;;;;;;;8DAIvC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAOzC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgB9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;wCAA0B;wCACpC;wCAAY;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,OAAO,OAAO,IAAI;8DAElB,cAAA,6LAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;mDALjC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAc5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;8CAKzC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;8CAKzC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,UAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAM;8BAEN,cAAA,6LAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;;;;;;;;;;;AAKrC;KAhPwB", "debugId": null}}]}