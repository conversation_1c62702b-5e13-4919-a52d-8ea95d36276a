// Mock data for NameCardAI MVP

export const features = [
  {
    id: 1,
    title: "AR Card Rendering",
    description: "Stunning 3D animated business cards with real-time AR effects",
    icon: "🎯",
    category: "core",
    demo: "ar-card"
  },
  {
    id: 2,
    title: "Multi-Share Methods",
    description: "QR, NFC, camera scan, or just remember a name/number",
    icon: "📱",
    category: "sharing",
    demo: "sharing-methods"
  },
  {
    id: 3,
    title: "Face Recognition",
    description: "Instant card display through facial recognition technology",
    icon: "👤",
    category: "ai",
    demo: "face-recognition"
  },
  {
    id: 4,
    title: "Custom Effects",
    description: "Choose from 50+ preset effects or create your own",
    icon: "✨",
    category: "customization",
    demo: "effects-gallery"
  },
  {
    id: 5,
    title: "No App Required",
    description: "Works directly in any web browser, no downloads needed",
    icon: "🌐",
    category: "accessibility",
    demo: "browser-demo"
  },
  {
    id: 6,
    title: "Analytics Dashboard",
    description: "Track views, interactions, and networking success",
    icon: "📊",
    category: "analytics",
    demo: "analytics"
  }
];

export const pricingPlans = [
  {
    id: "free",
    name: "Free",
    price: 0,
    period: "forever",
    description: "Perfect for getting started",
    features: [
      "1 AR business card",
      "Basic random effects",
      "QR code sharing",
      "Basic analytics",
      "NameCardAI branding"
    ],
    limitations: [
      "Limited customization",
      "Basic support only"
    ],
    cta: "Get Started Free",
    popular: false
  },
  {
    id: "pro",
    name: "Pro",
    price: 12,
    period: "month",
    description: "For professionals who want to stand out",
    features: [
      "5 AR business cards",
      "Choose from 10+ effects",
      "All sharing methods",
      "Custom intro templates",
      "Advanced analytics",
      "Remove branding",
      "Priority support"
    ],
    limitations: [],
    cta: "Start Pro Trial",
    popular: true
  },
  {
    id: "premium",
    name: "Premium",
    price: 25,
    period: "month",
    description: "Ultimate networking experience",
    features: [
      "Unlimited AR cards",
      "Custom AR effects",
      "3D avatar creation",
      "Video intro overlays",
      "Advanced analytics",
      "White-label options",
      "API access",
      "Dedicated support"
    ],
    limitations: [],
    cta: "Go Premium",
    popular: false
  }
];

export const testimonials = [
  {
    id: 1,
    name: "Sarah Chen",
    role: "Sales Director",
    company: "TechCorp",
    avatar: "/avatars/sarah.jpg",
    content: "NameCardAI completely transformed how I network. The AR effects are mind-blowing and people remember me instantly!",
    rating: 5,
    featured: true
  },
  {
    id: 2,
    name: "Marcus Rodriguez",
    role: "Freelance Designer",
    company: "Independent",
    avatar: "/avatars/marcus.jpg",
    content: "As a freelancer, standing out is crucial. NameCardAI helps me make unforgettable first impressions.",
    rating: 5,
    featured: true
  },
  {
    id: 3,
    name: "Dr. Emily Watson",
    role: "Startup Founder",
    company: "MedTech Solutions",
    avatar: "/avatars/emily.jpg",
    content: "The face recognition feature is incredible. Investors remember our pitch because of the innovative card experience.",
    rating: 5,
    featured: true
  },
  {
    id: 4,
    name: "James Park",
    role: "Event Organizer",
    company: "Global Events",
    avatar: "/avatars/james.jpg",
    content: "Perfect for conferences! Attendees love the interactive experience and it's eco-friendly too.",
    rating: 5,
    featured: false
  }
];

export const competitors = [
  {
    name: "HiHello",
    strengths: ["Clean UI", "Good integrations"],
    weaknesses: ["No AR features", "Limited customization"],
    pricing: "$12/month",
    marketShare: "15%"
  },
  {
    name: "Mobilo",
    strengths: ["NFC focus", "Good analytics"],
    weaknesses: ["Hardware dependency", "No 3D/AR"],
    pricing: "$6-15/month",
    marketShare: "10%"
  },
  {
    name: "Popl",
    strengths: ["Social integration", "Trendy design"],
    weaknesses: ["Limited professional features", "No AR"],
    pricing: "$5-10/month",
    marketShare: "8%"
  },
  {
    name: "Linq",
    strengths: ["NFC hardware", "Enterprise focus"],
    weaknesses: ["High cost", "No AR/3D features"],
    pricing: "$15-50/month",
    marketShare: "12%"
  }
];

export const roadmapItems = [
  {
    id: 1,
    phase: "MVP",
    title: "Core Platform Launch",
    description: "Web app with QR/Name/Camera scan, 3D card, preset effects",
    status: "in-progress",
    quarter: "Q1 2024",
    features: [
      "3D card renderer",
      "Basic AR effects",
      "QR code sharing",
      "Name-based search"
    ]
  },
  {
    id: 2,
    phase: "Phase 1",
    title: "User Dashboard",
    description: "Card editing, intro uploads, effect selection",
    status: "planned",
    quarter: "Q2 2024",
    features: [
      "Card customization",
      "Video intro upload",
      "Effect library",
      "Analytics dashboard"
    ]
  },
  {
    id: 3,
    phase: "Phase 2",
    title: "Live AR Mode",
    description: "Real-time camera scan and AR overlays",
    status: "planned",
    quarter: "Q3 2024",
    features: [
      "Camera integration",
      "Real-time AR",
      "Face matching",
      "Live overlays"
    ]
  },
  {
    id: 4,
    phase: "Phase 3",
    title: "Mobile PWA",
    description: "Progressive web app with offline capabilities",
    status: "planned",
    quarter: "Q4 2024",
    features: [
      "PWA installation",
      "Offline mode",
      "Push notifications",
      "Native feel"
    ]
  },
  {
    id: 5,
    phase: "Phase 4",
    title: "Enterprise Features",
    description: "Company bundles, CRM integration, advanced analytics",
    status: "planned",
    quarter: "Q1 2025",
    features: [
      "Team management",
      "CRM integration",
      "White-label options",
      "Enterprise security"
    ]
  }
];

export const effectTypes = [
  {
    id: "matrix",
    name: "Matrix Effect",
    description: "Falling green characters background",
    category: "background",
    preview: "/effects/matrix.gif"
  },
  {
    id: "particles",
    name: "Particle System",
    description: "Floating particles with physics",
    category: "background",
    preview: "/effects/particles.gif"
  },
  {
    id: "tilt",
    name: "3D Tilt",
    description: "Interactive 3D perspective on hover",
    category: "interaction",
    preview: "/effects/tilt.gif"
  },
  {
    id: "glow",
    name: "Neon Glow",
    description: "Pulsing neon border effects",
    category: "border",
    preview: "/effects/glow.gif"
  },
  {
    id: "typing",
    name: "Typing Animation",
    description: "Character-by-character text reveal",
    category: "text",
    preview: "/effects/typing.gif"
  },
  {
    id: "smoke",
    name: "Smoke Trail",
    description: "Particle-based smoke effects",
    category: "particle",
    preview: "/effects/smoke.gif"
  },
  {
    id: "fireflies",
    name: "Fireflies",
    description: "Floating light particles",
    category: "particle",
    preview: "/effects/fireflies.gif"
  },
  {
    id: "hologram",
    name: "Hologram",
    description: "Futuristic holographic display",
    category: "overlay",
    preview: "/effects/hologram.gif"
  }
];

export const demoCards = [
  {
    id: 1,
    name: "Alex Thompson",
    title: "Senior Developer",
    company: "TechFlow Inc.",
    email: "<EMAIL>",
    phone: "+****************",
    website: "alexthompson.dev",
    avatar: "/avatars/alex.jpg",
    background: "gradient-primary",
    effect: "matrix",
    theme: "dark"
  },
  {
    id: 2,
    name: "Maria Garcia",
    title: "UX Designer",
    company: "Creative Studio",
    email: "<EMAIL>",
    phone: "+****************",
    website: "mariagarcia.design",
    avatar: "/avatars/maria.jpg",
    background: "gradient-secondary",
    effect: "particles",
    theme: "light"
  },
  {
    id: 3,
    name: "David Kim",
    title: "Product Manager",
    company: "Innovation Labs",
    email: "<EMAIL>",
    phone: "+****************",
    website: "davidkim.pm",
    avatar: "/avatars/david.jpg",
    background: "gradient-accent",
    effect: "hologram",
    theme: "dark"
  }
];

export const stats = [
  {
    label: "Cards Created",
    value: "50,000+",
    icon: "📇"
  },
  {
    label: "Active Users",
    value: "12,500+",
    icon: "👥"
  },
  {
    label: "Connections Made",
    value: "250,000+",
    icon: "🤝"
  },
  {
    label: "Countries",
    value: "45+",
    icon: "🌍"
  }
];

export const faqs = [
  {
    id: 1,
    question: "Do I need to download an app?",
    answer: "No! NameCardAI works directly in any web browser. Recipients can view your AR card instantly without downloading anything."
  },
  {
    id: 2,
    question: "How does the face recognition work?",
    answer: "Our AI technology can recognize faces and instantly display the associated business card. It's completely privacy-focused and works offline."
  },
  {
    id: 3,
    question: "Can I customize the AR effects?",
    answer: "Yes! Choose from 50+ preset effects in our Pro plan, or create completely custom AR experiences with our Premium plan."
  },
  {
    id: 4,
    question: "Is my data secure?",
    answer: "Absolutely. We use enterprise-grade encryption and never share your personal information. You control who sees your card and when."
  },
  {
    id: 5,
    question: "What devices are supported?",
    answer: "NameCardAI works on any device with a modern web browser - smartphones, tablets, laptops, and desktops."
  }
];
