import { StateCreator, StoreMutatorIdentifier } from '../vanilla';
type Write<T, U> = Pick<T, Exclude<keyof T, keyof U>> & U;
type Combine = <T extends object, U extends object, <PERSON><PERSON> extends [
    StoreMutatorIdentifier,
    unknown
][] = [
], <PERSON><PERSON> extends [
    StoreMutatorIdentifier,
    unknown
][] = [
]>(initialState: T, additionalStateCreator: StateCreator<T, Mps, Mcs, U>) => StateCreator<Write<T, U>, Mps, Mcs>;
export declare const combine: Combine;
export {};
