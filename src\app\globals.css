@import "tailwindcss";

:root {
  /* Futuristic Color Palette */
  --primary: #00f5ff;
  --secondary: #ff0080;
  --accent: #00ff41;
  --background: #0a0a0a;
  --surface: #1a1a1a;
  --surface-light: #2a2a2a;
  --text: #ffffff;
  --text-muted: #a0a0a0;
  --text-dim: #666666;

  /* Gradients */
  --gradient-primary: linear-gradient(45deg, #00f5ff, #ff0080);
  --gradient-secondary: linear-gradient(135deg, #00ff41, #00f5ff);
  --gradient-accent: linear-gradient(90deg, #ff0080, #00f5ff, #00ff41);

  /* Shadows and Glows */
  --glow-primary: 0 0 20px rgba(0, 245, 255, 0.5);
  --glow-secondary: 0 0 20px rgba(255, 0, 128, 0.5);
  --glow-accent: 0 0 20px rgba(0, 255, 65, 0.5);

  /* Animation Durations */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
}

@theme inline {
  /* Colors */
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-background: var(--background);
  --color-surface: var(--surface);
  --color-surface-light: var(--surface-light);
  --color-text: var(--text);
  --color-text-muted: var(--text-muted);
  --color-text-dim: var(--text-dim);

  /* Fonts */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Orbitron', monospace;
  --font-mono: 'Fira Code', monospace;

  /* Custom Utilities */
  --color-glow-primary: var(--glow-primary);
  --color-glow-secondary: var(--glow-secondary);
  --color-glow-accent: var(--glow-accent);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--text);
  font-family: var(--font-sans);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Selection */
::selection {
  background: var(--primary);
  color: var(--background);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-border {
  border: 1px solid var(--primary);
  box-shadow: 0 0 10px var(--primary);
}

/* Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-matrix {
  animation: matrix 20s linear infinite;
}

/* Keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px var(--primary); }
  50% { box-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary); }
}

@keyframes matrix {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}
