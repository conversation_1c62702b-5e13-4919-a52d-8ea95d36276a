{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Utility function to merge Tailwind CSS classes\n * @param {...string} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generate a random number between min and max\n * @param {number} min - Minimum value\n * @param {number} max - Maximum value\n * @returns {number} Random number\n */\nexport function randomBetween(min, max) {\n  return Math.random() * (max - min) + min;\n}\n\n/**\n * Debounce function to limit function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Time limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n/**\n * Format number with commas\n * @param {number} num - Number to format\n * @returns {string} Formatted number\n */\nexport function formatNumber(num) {\n  return new Intl.NumberFormat().format(num);\n}\n\n/**\n * Generate a unique ID\n * @returns {string} Unique ID\n */\nexport function generateId() {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * Check if device is mobile\n * @returns {boolean} True if mobile\n */\nexport function isMobile() {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n}\n\n/**\n * Check if device supports touch\n * @returns {boolean} True if touch supported\n */\nexport function isTouchDevice() {\n  if (typeof window === 'undefined') return false;\n  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n}\n\n/**\n * Smooth scroll to element\n * @param {string} elementId - ID of element to scroll to\n * @param {number} offset - Offset from top\n */\nexport function scrollToElement(elementId, offset = 0) {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Copy text to clipboard\n * @param {string} text - Text to copy\n * @returns {Promise<boolean>} Success status\n */\nexport async function copyToClipboard(text) {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (err) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n/**\n * Get random item from array\n * @param {Array} array - Array to pick from\n * @returns {*} Random item\n */\nexport function getRandomItem(array) {\n  return array[Math.floor(Math.random() * array.length)];\n}\n\n/**\n * Shuffle array\n * @param {Array} array - Array to shuffle\n * @returns {Array} Shuffled array\n */\nexport function shuffleArray(array) {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * Convert hex color to RGB\n * @param {string} hex - Hex color\n * @returns {object} RGB values\n */\nexport function hexToRgb(hex) {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n}\n\n/**\n * Validate email address\n * @param {string} email - Email to validate\n * @returns {boolean} Valid email\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Format date\n * @param {Date|string} date - Date to format\n * @param {string} locale - Locale for formatting\n * @returns {string} Formatted date\n */\nexport function formatDate(date, locale = 'en-US') {\n  return new Intl.DateTimeFormat(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(new Date(date));\n}\n\n/**\n * Calculate reading time\n * @param {string} text - Text to calculate reading time for\n * @param {number} wpm - Words per minute (default: 200)\n * @returns {number} Reading time in minutes\n */\nexport function calculateReadingTime(text, wpm = 200) {\n  const words = text.trim().split(/\\s+/).length;\n  return Math.ceil(words / wpm);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,cAAc,GAAG,EAAE,GAAG;IACpC,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;AACvC;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAOO,SAAS,aAAa,GAAG;IAC9B,OAAO,IAAI,KAAK,YAAY,GAAG,MAAM,CAAC;AACxC;AAMO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAMO,SAAS;IACd,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAMO,SAAS;IACd,uCAAmC;;IAAY;IAC/C,OAAO,kBAAkB,UAAU,UAAU,cAAc,GAAG;AAChE;AAOO,SAAS,gBAAgB,SAAS,EAAE,SAAS,CAAC;IACnD,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAOO,eAAe,gBAAgB,IAAI;IACxC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAOO,SAAS,cAAc,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAOO,SAAS,SAAS,GAAG;IAC1B,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,OAAO,SAAS;QACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IACzB,IAAI;AACN;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAQO,SAAS,WAAW,IAAI,EAAE,SAAS,OAAO;IAC/C,OAAO,IAAI,KAAK,cAAc,CAAC,QAAQ;QACrC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAQO,SAAS,qBAAqB,IAAI,EAAE,MAAM,GAAG;IAClD,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\nconst buttonVariants = {\n  default: \"bg-primary text-background hover:bg-primary/90 glow-primary\",\n  secondary: \"bg-secondary text-background hover:bg-secondary/90 glow-secondary\",\n  accent: \"bg-accent text-background hover:bg-accent/90 glow-accent\",\n  outline: \"border border-primary text-primary hover:bg-primary hover:text-background neon-border\",\n  ghost: \"text-primary hover:bg-primary/10 hover:text-primary\",\n  gradient: \"bg-gradient-to-r from-primary to-secondary text-background hover:from-primary/90 hover:to-secondary/90\",\n  glass: \"glass text-text hover:bg-white/20\"\n};\n\nconst buttonSizes = {\n  sm: \"h-9 px-3 text-sm\",\n  default: \"h-11 px-6 py-2\",\n  lg: \"h-12 px-8 text-lg\",\n  xl: \"h-14 px-10 text-xl\"\n};\n\nexport default function Button({\n  children,\n  className,\n  variant = \"default\",\n  size = \"default\",\n  disabled = false,\n  loading = false,\n  onClick,\n  type = \"button\",\n  ...props\n}) {\n  return (\n    <button\n      type={type}\n      className={cn(\n        // Base styles\n        \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300\",\n        \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\",\n        \"disabled:pointer-events-none disabled:opacity-50\",\n        \"transform hover:scale-105 active:scale-95\",\n        \n        // Variants\n        buttonVariants[variant],\n        \n        // Sizes\n        buttonSizes[size],\n        \n        // Loading state\n        loading && \"cursor-not-allowed opacity-70\",\n        \n        // Custom className\n        className\n      )}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"mr-2 h-4 w-4 animate-spin\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n}\n\n// Specialized button components\nexport function PrimaryButton({ children, ...props }) {\n  return (\n    <Button variant=\"default\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function SecondaryButton({ children, ...props }) {\n  return (\n    <Button variant=\"secondary\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function GradientButton({ children, ...props }) {\n  return (\n    <Button variant=\"gradient\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function OutlineButton({ children, ...props }) {\n  return (\n    <Button variant=\"outline\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function GlassButton({ children, ...props }) {\n  return (\n    <Button variant=\"glass\" {...props}>\n      {children}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,QAAQ;IACR,SAAS;IACT,OAAO;IACP,UAAU;IACV,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,OAAO,EAC7B,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,8FACA,0GACA,oDACA,6CAEA,WAAW;QACX,cAAc,CAAC,QAAQ,EAEvB,QAAQ;QACR,WAAW,CAAC,KAAK,EAEjB,gBAAgB;QAChB,WAAW,iCAEX,mBAAmB;QACnB;QAEF,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA9DwB;AAiEjB,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClD,qBACE,6LAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAChC;;;;;;AAGP;MANgB;AAQT,SAAS,gBAAgB,EAAE,QAAQ,EAAE,GAAG,OAAO;IACpD,qBACE,6LAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAClC;;;;;;AAGP;MANgB;AAQT,SAAS,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO;IACnD,qBACE,6LAAC;QAAO,SAAQ;QAAY,GAAG,KAAK;kBACjC;;;;;;AAGP;MANgB;AAQT,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClD,qBACE,6LAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAChC;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChD,qBACE,6LAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAC9B;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'Pitch', href: '/pitch' },\n    { name: 'Why Us', href: '/why-us' },\n    { name: 'Roadmap', href: '/roadmap' },\n  ];\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled \n          ? 'glass backdrop-blur-md border-b border-white/10' \n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300\">\n              <svg\n                className=\"w-6 h-6 text-background\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-bold font-display text-gradient\">\n              NameCardAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-text-muted hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full transition-all duration-300\" />\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"gradient\" size=\"sm\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2 rounded-lg text-text-muted hover:text-primary transition-colors\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMobileMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 glass backdrop-blur-md border-b border-white/10\">\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block text-text-muted hover:text-primary transition-colors duration-300 font-medium py-2\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 space-y-3\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">\n                  Sign In\n                </Button>\n                <Button variant=\"gradient\" size=\"sm\" className=\"w-full\">\n                  Get Started\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,oDACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;kDAER,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,6LAAC;oCAAK,WAAU;8CAA+C;;;;;;;;;;;;sCAMjE,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAW,MAAK;8CAAK;;;;;;;;;;;;sCAMvC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,oBAAoB,CAAC;sCAEpC,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,iCACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAAS;;;;;;kDAGrD,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAW,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GAlIwB;KAAA", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/DemoHeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport Button, { GradientButton } from '@/components/ui/Button';\n\nexport default function DemoHeroSection() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden pt-16\">\n      {/* Animated Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-background via-surface to-background\">\n        {/* Holographic Grid */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"absolute inset-0\" style={{\n            backgroundImage: `linear-gradient(rgba(0, 245, 255, 0.3) 1px, transparent 1px), \n                             linear-gradient(90deg, rgba(0, 245, 255, 0.3) 1px, transparent 1px)`,\n            backgroundSize: '50px 50px',\n            animation: 'matrix 30s linear infinite'\n          }} />\n        </div>\n\n        {/* Floating AR Elements */}\n        <div className=\"absolute inset-0\">\n          {[...Array(15)].map((_, i) => (\n            <div\n              key={i}\n              className={cn(\n                \"absolute rounded-lg opacity-60 animate-float\",\n                i % 3 === 0 ? \"bg-primary/20 border border-primary/50\" : \n                i % 3 === 1 ? \"bg-secondary/20 border border-secondary/50\" : \n                \"bg-accent/20 border border-accent/50\"\n              )}\n              style={{\n                width: `${30 + Math.random() * 40}px`,\n                height: `${20 + Math.random() * 30}px`,\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 3}s`,\n                animationDuration: `${4 + Math.random() * 2}s`\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className={cn(\n          \"transition-all duration-1000\",\n          isLoaded ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n        )}>\n          {/* Main Headline */}\n          <div className=\"space-y-6 mb-12\">\n            <div className=\"inline-block bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full px-6 py-2 border border-primary/30\">\n              <span className=\"text-primary font-medium\">🎯 Live Interactive Demo</span>\n            </div>\n            \n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold font-display\">\n              <span className=\"text-gradient\">Experience AR</span>\n              <br />\n              <span className=\"text-text\">Business Cards</span>\n              <br />\n              <span className=\"text-gradient-secondary\">In Action</span>\n            </h1>\n            \n            <p className=\"text-xl md:text-2xl text-text-muted max-w-4xl mx-auto leading-relaxed\">\n              Create, customize, and share stunning 3D digital business cards with real-time AR effects. \n              No downloads required—experience the future of networking right in your browser.\n            </p>\n          </div>\n\n          {/* Demo Features Grid */}\n          <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n            <div className=\"glass p-6 rounded-xl neon-border hover:scale-105 transition-transform duration-300\">\n              <div className=\"text-4xl mb-4\">🎨</div>\n              <h3 className=\"text-xl font-semibold text-gradient mb-2\">Real-Time Customization</h3>\n              <p className=\"text-text-muted\">Design your card with live preview and instant updates</p>\n            </div>\n            \n            <div className=\"glass p-6 rounded-xl neon-border hover:scale-105 transition-transform duration-300\">\n              <div className=\"text-4xl mb-4\">🚀</div>\n              <h3 className=\"text-xl font-semibold text-gradient mb-2\">AR Effects Library</h3>\n              <p className=\"text-text-muted\">Choose from 50+ stunning effects and animations</p>\n            </div>\n            \n            <div className=\"glass p-6 rounded-xl neon-border hover:scale-105 transition-transform duration-300\">\n              <div className=\"text-4xl mb-4\">📱</div>\n              <h3 className=\"text-xl font-semibold text-gradient mb-2\">Multi-Share Options</h3>\n              <p className=\"text-text-muted\">QR, NFC, camera scan, or name-based sharing</p>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center mb-12\">\n            <GradientButton size=\"xl\" className=\"group\">\n              <span className=\"mr-3 text-2xl\">🎮</span>\n              Start Creating Now\n              <svg\n                className=\"ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M13 7l5 5m0 0l-5 5m5-5H6\"\n                />\n              </svg>\n            </GradientButton>\n            \n            <Button variant=\"outline\" size=\"xl\" className=\"group\">\n              <span className=\"mr-3 text-2xl\">👁️</span>\n              Watch Tutorial\n              <svg\n                className=\"ml-3 w-6 h-6 group-hover:scale-110 transition-transform\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M8 5v14l11-7z\" />\n              </svg>\n            </Button>\n          </div>\n\n          {/* Demo Stats */}\n          <div className=\"glass p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-6\">\n              Join the AR Revolution\n            </h3>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gradient mb-2\">50K+</div>\n                <div className=\"text-text-muted text-sm\">Cards Created</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gradient mb-2\">98%</div>\n                <div className=\"text-text-muted text-sm\">User Satisfaction</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gradient mb-2\">45+</div>\n                <div className=\"text-text-muted text-sm\">Countries</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gradient mb-2\">24/7</div>\n                <div className=\"text-text-muted text-sm\">Support</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"flex flex-col items-center space-y-2\">\n          <div className=\"text-text-muted text-sm\">Try the demo below</div>\n          <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-primary rounded-full mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,YAAY;QACd;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAmB,OAAO;gCACvC,iBAAiB,CAAC;gGACkE,CAAC;gCACrF,gBAAgB;gCAChB,WAAW;4BACb;;;;;;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;gCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,IAAI,MAAM,IAAI,2CACd,IAAI,MAAM,IAAI,+CACd;gCAEF,OAAO;oCACL,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;oCACrC,QAAQ,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;oCACtC,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gCAChD;+BAdK;;;;;;;;;;;;;;;;0BAqBb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gCACA,WAAW,8BAA8B;;sCAGzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;8CAG7C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;;8CAG5C,6LAAC;oCAAE,WAAU;8CAAwE;;;;;;;;;;;;sCAOvF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAGjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAGjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,iBAAc;oCAAC,MAAK;oCAAK,WAAU;;sDAClC,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAS;sDAEzC,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;8CAKR,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAU;sDAE1C,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA0B;;;;;;sCACzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAvKwB;KAAA", "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/lib/data.js"], "sourcesContent": ["// Mock data for NameCardAI MVP\n\nexport const features = [\n  {\n    id: 1,\n    title: \"AR Card Rendering\",\n    description: \"Stunning 3D animated business cards with real-time AR effects\",\n    icon: \"🎯\",\n    category: \"core\",\n    demo: \"ar-card\"\n  },\n  {\n    id: 2,\n    title: \"Multi-Share Methods\",\n    description: \"QR, NFC, camera scan, or just remember a name/number\",\n    icon: \"📱\",\n    category: \"sharing\",\n    demo: \"sharing-methods\"\n  },\n  {\n    id: 3,\n    title: \"Face Recognition\",\n    description: \"Instant card display through facial recognition technology\",\n    icon: \"👤\",\n    category: \"ai\",\n    demo: \"face-recognition\"\n  },\n  {\n    id: 4,\n    title: \"Custom Effects\",\n    description: \"Choose from 50+ preset effects or create your own\",\n    icon: \"✨\",\n    category: \"customization\",\n    demo: \"effects-gallery\"\n  },\n  {\n    id: 5,\n    title: \"No App Required\",\n    description: \"Works directly in any web browser, no downloads needed\",\n    icon: \"🌐\",\n    category: \"accessibility\",\n    demo: \"browser-demo\"\n  },\n  {\n    id: 6,\n    title: \"Analytics Dashboard\",\n    description: \"Track views, interactions, and networking success\",\n    icon: \"📊\",\n    category: \"analytics\",\n    demo: \"analytics\"\n  }\n];\n\nexport const pricingPlans = [\n  {\n    id: \"free\",\n    name: \"Free\",\n    price: 0,\n    period: \"forever\",\n    description: \"Perfect for getting started\",\n    features: [\n      \"1 AR business card\",\n      \"Basic random effects\",\n      \"QR code sharing\",\n      \"Basic analytics\",\n      \"NameCardAI branding\"\n    ],\n    limitations: [\n      \"Limited customization\",\n      \"Basic support only\"\n    ],\n    cta: \"Get Started Free\",\n    popular: false\n  },\n  {\n    id: \"pro\",\n    name: \"Pro\",\n    price: 12,\n    period: \"month\",\n    description: \"For professionals who want to stand out\",\n    features: [\n      \"5 AR business cards\",\n      \"Choose from 10+ effects\",\n      \"All sharing methods\",\n      \"Custom intro templates\",\n      \"Advanced analytics\",\n      \"Remove branding\",\n      \"Priority support\"\n    ],\n    limitations: [],\n    cta: \"Start Pro Trial\",\n    popular: true\n  },\n  {\n    id: \"premium\",\n    name: \"Premium\",\n    price: 25,\n    period: \"month\",\n    description: \"Ultimate networking experience\",\n    features: [\n      \"Unlimited AR cards\",\n      \"Custom AR effects\",\n      \"3D avatar creation\",\n      \"Video intro overlays\",\n      \"Advanced analytics\",\n      \"White-label options\",\n      \"API access\",\n      \"Dedicated support\"\n    ],\n    limitations: [],\n    cta: \"Go Premium\",\n    popular: false\n  }\n];\n\nexport const testimonials = [\n  {\n    id: 1,\n    name: \"Sarah Chen\",\n    role: \"Sales Director\",\n    company: \"TechCorp\",\n    avatar: \"/avatars/sarah.jpg\",\n    content: \"NameCardAI completely transformed how I network. The AR effects are mind-blowing and people remember me instantly!\",\n    rating: 5,\n    featured: true\n  },\n  {\n    id: 2,\n    name: \"Marcus Rodriguez\",\n    role: \"Freelance Designer\",\n    company: \"Independent\",\n    avatar: \"/avatars/marcus.jpg\",\n    content: \"As a freelancer, standing out is crucial. NameCardAI helps me make unforgettable first impressions.\",\n    rating: 5,\n    featured: true\n  },\n  {\n    id: 3,\n    name: \"Dr. Emily Watson\",\n    role: \"Startup Founder\",\n    company: \"MedTech Solutions\",\n    avatar: \"/avatars/emily.jpg\",\n    content: \"The face recognition feature is incredible. Investors remember our pitch because of the innovative card experience.\",\n    rating: 5,\n    featured: true\n  },\n  {\n    id: 4,\n    name: \"James Park\",\n    role: \"Event Organizer\",\n    company: \"Global Events\",\n    avatar: \"/avatars/james.jpg\",\n    content: \"Perfect for conferences! Attendees love the interactive experience and it's eco-friendly too.\",\n    rating: 5,\n    featured: false\n  }\n];\n\nexport const competitors = [\n  {\n    name: \"HiHello\",\n    strengths: [\"Clean UI\", \"Good integrations\"],\n    weaknesses: [\"No AR features\", \"Limited customization\"],\n    pricing: \"$12/month\",\n    marketShare: \"15%\"\n  },\n  {\n    name: \"Mobilo\",\n    strengths: [\"NFC focus\", \"Good analytics\"],\n    weaknesses: [\"Hardware dependency\", \"No 3D/AR\"],\n    pricing: \"$6-15/month\",\n    marketShare: \"10%\"\n  },\n  {\n    name: \"Popl\",\n    strengths: [\"Social integration\", \"Trendy design\"],\n    weaknesses: [\"Limited professional features\", \"No AR\"],\n    pricing: \"$5-10/month\",\n    marketShare: \"8%\"\n  },\n  {\n    name: \"Linq\",\n    strengths: [\"NFC hardware\", \"Enterprise focus\"],\n    weaknesses: [\"High cost\", \"No AR/3D features\"],\n    pricing: \"$15-50/month\",\n    marketShare: \"12%\"\n  }\n];\n\nexport const roadmapItems = [\n  {\n    id: 1,\n    phase: \"MVP\",\n    title: \"Core Platform Launch\",\n    description: \"Web app with QR/Name/Camera scan, 3D card, preset effects\",\n    status: \"in-progress\",\n    quarter: \"Q1 2024\",\n    features: [\n      \"3D card renderer\",\n      \"Basic AR effects\",\n      \"QR code sharing\",\n      \"Name-based search\"\n    ]\n  },\n  {\n    id: 2,\n    phase: \"Phase 1\",\n    title: \"User Dashboard\",\n    description: \"Card editing, intro uploads, effect selection\",\n    status: \"planned\",\n    quarter: \"Q2 2024\",\n    features: [\n      \"Card customization\",\n      \"Video intro upload\",\n      \"Effect library\",\n      \"Analytics dashboard\"\n    ]\n  },\n  {\n    id: 3,\n    phase: \"Phase 2\",\n    title: \"Live AR Mode\",\n    description: \"Real-time camera scan and AR overlays\",\n    status: \"planned\",\n    quarter: \"Q3 2024\",\n    features: [\n      \"Camera integration\",\n      \"Real-time AR\",\n      \"Face matching\",\n      \"Live overlays\"\n    ]\n  },\n  {\n    id: 4,\n    phase: \"Phase 3\",\n    title: \"Mobile PWA\",\n    description: \"Progressive web app with offline capabilities\",\n    status: \"planned\",\n    quarter: \"Q4 2024\",\n    features: [\n      \"PWA installation\",\n      \"Offline mode\",\n      \"Push notifications\",\n      \"Native feel\"\n    ]\n  },\n  {\n    id: 5,\n    phase: \"Phase 4\",\n    title: \"Enterprise Features\",\n    description: \"Company bundles, CRM integration, advanced analytics\",\n    status: \"planned\",\n    quarter: \"Q1 2025\",\n    features: [\n      \"Team management\",\n      \"CRM integration\",\n      \"White-label options\",\n      \"Enterprise security\"\n    ]\n  }\n];\n\nexport const effectTypes = [\n  {\n    id: \"matrix\",\n    name: \"Matrix Effect\",\n    description: \"Falling green characters background\",\n    category: \"background\",\n    preview: \"/effects/matrix.gif\"\n  },\n  {\n    id: \"particles\",\n    name: \"Particle System\",\n    description: \"Floating particles with physics\",\n    category: \"background\",\n    preview: \"/effects/particles.gif\"\n  },\n  {\n    id: \"tilt\",\n    name: \"3D Tilt\",\n    description: \"Interactive 3D perspective on hover\",\n    category: \"interaction\",\n    preview: \"/effects/tilt.gif\"\n  },\n  {\n    id: \"glow\",\n    name: \"Neon Glow\",\n    description: \"Pulsing neon border effects\",\n    category: \"border\",\n    preview: \"/effects/glow.gif\"\n  },\n  {\n    id: \"typing\",\n    name: \"Typing Animation\",\n    description: \"Character-by-character text reveal\",\n    category: \"text\",\n    preview: \"/effects/typing.gif\"\n  },\n  {\n    id: \"smoke\",\n    name: \"Smoke Trail\",\n    description: \"Particle-based smoke effects\",\n    category: \"particle\",\n    preview: \"/effects/smoke.gif\"\n  },\n  {\n    id: \"fireflies\",\n    name: \"Fireflies\",\n    description: \"Floating light particles\",\n    category: \"particle\",\n    preview: \"/effects/fireflies.gif\"\n  },\n  {\n    id: \"hologram\",\n    name: \"Hologram\",\n    description: \"Futuristic holographic display\",\n    category: \"overlay\",\n    preview: \"/effects/hologram.gif\"\n  }\n];\n\nexport const demoCards = [\n  {\n    id: 1,\n    name: \"Alex Thompson\",\n    title: \"Senior Developer\",\n    company: \"TechFlow Inc.\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    website: \"alexthompson.dev\",\n    avatar: \"/avatars/alex.jpg\",\n    background: \"gradient-primary\",\n    effect: \"matrix\",\n    theme: \"dark\"\n  },\n  {\n    id: 2,\n    name: \"Maria Garcia\",\n    title: \"UX Designer\",\n    company: \"Creative Studio\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    website: \"mariagarcia.design\",\n    avatar: \"/avatars/maria.jpg\",\n    background: \"gradient-secondary\",\n    effect: \"particles\",\n    theme: \"light\"\n  },\n  {\n    id: 3,\n    name: \"David Kim\",\n    title: \"Product Manager\",\n    company: \"Innovation Labs\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    website: \"davidkim.pm\",\n    avatar: \"/avatars/david.jpg\",\n    background: \"gradient-accent\",\n    effect: \"hologram\",\n    theme: \"dark\"\n  }\n];\n\nexport const stats = [\n  {\n    label: \"Cards Created\",\n    value: \"50,000+\",\n    icon: \"📇\"\n  },\n  {\n    label: \"Active Users\",\n    value: \"12,500+\",\n    icon: \"👥\"\n  },\n  {\n    label: \"Connections Made\",\n    value: \"250,000+\",\n    icon: \"🤝\"\n  },\n  {\n    label: \"Countries\",\n    value: \"45+\",\n    icon: \"🌍\"\n  }\n];\n\nexport const faqs = [\n  {\n    id: 1,\n    question: \"Do I need to download an app?\",\n    answer: \"No! NameCardAI works directly in any web browser. Recipients can view your AR card instantly without downloading anything.\"\n  },\n  {\n    id: 2,\n    question: \"How does the face recognition work?\",\n    answer: \"Our AI technology can recognize faces and instantly display the associated business card. It's completely privacy-focused and works offline.\"\n  },\n  {\n    id: 3,\n    question: \"Can I customize the AR effects?\",\n    answer: \"Yes! Choose from 50+ preset effects in our Pro plan, or create completely custom AR experiences with our Premium plan.\"\n  },\n  {\n    id: 4,\n    question: \"Is my data secure?\",\n    answer: \"Absolutely. We use enterprise-grade encryption and never share your personal information. You control who sees your card and when.\"\n  },\n  {\n    id: 5,\n    question: \"What devices are supported?\",\n    answer: \"NameCardAI works on any device with a modern web browser - smartphones, tablets, laptops, and desktops.\"\n  }\n];\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,WAAW;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa,EAAE;QACf,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa,EAAE;QACf,KAAK;QACL,SAAS;IACX;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,MAAM,cAAc;IACzB;QACE,MAAM;QACN,WAAW;YAAC;YAAY;SAAoB;QAC5C,YAAY;YAAC;YAAkB;SAAwB;QACvD,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,WAAW;YAAC;YAAa;SAAiB;QAC1C,YAAY;YAAC;YAAuB;SAAW;QAC/C,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,WAAW;YAAC;YAAsB;SAAgB;QAClD,YAAY;YAAC;YAAiC;SAAQ;QACtD,SAAS;QACT,aAAa;IACf;IACA;QACE,MAAM;QACN,WAAW;YAAC;YAAgB;SAAmB;QAC/C,YAAY;YAAC;YAAa;SAAoB;QAC9C,SAAS;QACT,aAAa;IACf;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,SAAS;IACX;CACD;AAEM,MAAM,YAAY;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD;AAEM,MAAM,OAAO;IAClB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Card.js"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\nconst cardVariants = {\n  default: \"bg-surface border border-surface-light\",\n  glass: \"glass\",\n  gradient: \"bg-gradient-to-br from-surface to-surface-light\",\n  neon: \"bg-surface neon-border\",\n  floating: \"bg-surface border border-surface-light shadow-2xl animate-float\"\n};\n\nexport default function Card({\n  children,\n  className,\n  variant = \"default\",\n  hover = true,\n  glow = false,\n  ...props\n}) {\n  return (\n    <div\n      className={cn(\n        // Base styles\n        \"rounded-xl p-6 transition-all duration-300\",\n        \n        // Hover effects\n        hover && \"hover:scale-105 hover:shadow-2xl cursor-pointer\",\n        \n        // Glow effect\n        glow && \"glow-primary\",\n        \n        // Variants\n        cardVariants[variant],\n        \n        // Custom className\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\nexport function CardHeader({ children, className, ...props }) {\n  return (\n    <div\n      className={cn(\"flex flex-col space-y-1.5 pb-4\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\nexport function CardTitle({ children, className, ...props }) {\n  return (\n    <h3\n      className={cn(\n        \"text-2xl font-semibold leading-none tracking-tight text-gradient\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </h3>\n  );\n}\n\nexport function CardDescription({ children, className, ...props }) {\n  return (\n    <p\n      className={cn(\"text-sm text-text-muted\", className)}\n      {...props}\n    >\n      {children}\n    </p>\n  );\n}\n\nexport function CardContent({ children, className, ...props }) {\n  return (\n    <div className={cn(\"pt-0\", className)} {...props}>\n      {children}\n    </div>\n  );\n}\n\nexport function CardFooter({ children, className, ...props }) {\n  return (\n    <div\n      className={cn(\"flex items-center pt-4\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\n// Specialized card components\nexport function FeatureCard({ icon, title, description, className, ...props }) {\n  return (\n    <Card variant=\"glass\" hover glow className={cn(\"text-center\", className)} {...props}>\n      <CardContent>\n        <div className=\"text-4xl mb-4\">{icon}</div>\n        <CardTitle className=\"mb-2\">{title}</CardTitle>\n        <CardDescription>{description}</CardDescription>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport function PricingCard({ \n  plan, \n  price, \n  period, \n  features, \n  popular = false, \n  className, \n  ...props \n}) {\n  return (\n    <Card \n      variant={popular ? \"neon\" : \"glass\"} \n      hover \n      glow={popular}\n      className={cn(\n        \"relative h-full\",\n        popular && \"scale-105 border-2 border-primary\",\n        className\n      )} \n      {...props}\n    >\n      {popular && (\n        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n          <span className=\"bg-gradient-to-r from-primary to-secondary text-background px-4 py-1 rounded-full text-sm font-medium\">\n            Most Popular\n          </span>\n        </div>\n      )}\n      \n      <CardHeader>\n        <CardTitle>{plan}</CardTitle>\n        <div className=\"text-3xl font-bold text-gradient\">\n          ${price}\n          <span className=\"text-sm text-text-muted font-normal\">/{period}</span>\n        </div>\n      </CardHeader>\n      \n      <CardContent>\n        <ul className=\"space-y-3\">\n          {features.map((feature, index) => (\n            <li key={index} className=\"flex items-center\">\n              <span className=\"text-accent mr-2\">✓</span>\n              <span className=\"text-text-muted\">{feature}</span>\n            </li>\n          ))}\n        </ul>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport function TestimonialCard({ \n  name, \n  role, \n  company, \n  content, \n  avatar, \n  rating = 5,\n  className, \n  ...props \n}) {\n  return (\n    <Card variant=\"glass\" hover className={cn(\"h-full\", className)} {...props}>\n      <CardContent>\n        <div className=\"flex items-center mb-4\">\n          {[...Array(rating)].map((_, i) => (\n            <span key={i} className=\"text-accent text-lg\">★</span>\n          ))}\n        </div>\n        \n        <p className=\"text-text-muted mb-6 italic\">\"{content}\"</p>\n        \n        <div className=\"flex items-center\">\n          <div className=\"w-12 h-12 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-background font-bold mr-4\">\n            {name.charAt(0)}\n          </div>\n          <div>\n            <div className=\"font-semibold text-text\">{name}</div>\n            <div className=\"text-sm text-text-muted\">{role} at {company}</div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,OAAO;IACP,UAAU;IACV,MAAM;IACN,UAAU;AACZ;AAEe,SAAS,KAAK,EAC3B,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,8CAEA,gBAAgB;QAChB,SAAS,mDAET,cAAc;QACd,QAAQ,gBAER,WAAW;QACX,YAAY,CAAC,QAAQ,EAErB,mBAAmB;QACnB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA/BwB;AAiCjB,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC1D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IACzD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;MAZgB;AAcT,SAAS,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC3D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;kBAC7C;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC1D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAYT,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAO;IAC3E,qBACE,6LAAC;QAAK,SAAQ;QAAQ,KAAK;QAAC,IAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;QAAa,GAAG,KAAK;kBACjF,cAAA,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;8BAAiB;;;;;;8BAChC,6LAAC;oBAAU,WAAU;8BAAQ;;;;;;8BAC7B,6LAAC;8BAAiB;;;;;;;;;;;;;;;;;AAI1B;MAVgB;AAYT,SAAS,YAAY,EAC1B,IAAI,EACJ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,UAAU,KAAK,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,SAAS,UAAU,SAAS;QAC5B,KAAK;QACL,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mBACA,WAAW,qCACX;QAED,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAAwG;;;;;;;;;;;0BAM5H,6LAAC;;kCACC,6LAAC;kCAAW;;;;;;kCACZ,6LAAC;wBAAI,WAAU;;4BAAmC;4BAC9C;0CACF,6LAAC;gCAAK,WAAU;;oCAAsC;oCAAE;;;;;;;;;;;;;;;;;;;0BAI5D,6LAAC;0BACC,cAAA,6LAAC;oBAAG,WAAU;8BACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAAe,WAAU;;8CACxB,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;2BAF5B;;;;;;;;;;;;;;;;;;;;;AASrB;MAjDgB;AAmDT,SAAS,gBAAgB,EAC9B,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,CAAC,EACV,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QAAK,SAAQ;QAAQ,KAAK;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QAAa,GAAG,KAAK;kBACvE,cAAA,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC1B,6LAAC;4BAAa,WAAU;sCAAsB;2BAAnC;;;;;;;;;;8BAIf,6LAAC;oBAAE,WAAU;;wBAA8B;wBAAE;wBAAQ;;;;;;;8BAErD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,CAAC;;;;;;sCAEf,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAA2B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;;wCAA2B;wCAAK;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;MAjCgB", "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/ARCardSimulator.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport { demoCards, effectTypes } from '@/lib/data';\nimport Button, { GradientButton } from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\n\nexport default function ARCardSimulator() {\n  const [selectedCard, setSelectedCard] = useState(demoCards[0]);\n  const [selectedEffect, setSelectedEffect] = useState(effectTypes[0]);\n  const [isCustomizing, setIsCustomizing] = useState(false);\n  const [cardData, setCardData] = useState(demoCards[0]);\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const handleCardDataChange = (field, value) => {\n    setCardData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleEffectChange = (effect) => {\n    setSelectedEffect(effect);\n    setCardData(prev => ({\n      ...prev,\n      effect: effect.id\n    }));\n  };\n\n  const exportCard = () => {\n    // Simulate export functionality\n    alert('Card exported successfully! (Demo simulation)');\n  };\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-surface relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 via-transparent to-secondary/20\" />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">AR Card Simulator</span>\n            <br />\n            <span className=\"text-text\">Create Your Digital Identity</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto\">\n            Design and customize your AR-enhanced business card with real-time preview. \n            Experience the power of 3D animations and interactive effects.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Card Preview */}\n          <div className={cn(\n            \"transition-all duration-700\",\n            isVisible ? \"opacity-100 translate-x-0\" : \"opacity-0 -translate-x-10\"\n          )}>\n            <div className=\"sticky top-8\">\n              <h3 className=\"text-2xl font-bold text-gradient mb-6 text-center\">\n                Live Preview\n              </h3>\n              \n              {/* 3D Card Container */}\n              <div className=\"relative perspective-1000\">\n                <div className=\"relative transform-gpu transition-transform duration-700 hover:rotateY-12 hover:rotateX-5\">\n                  {/* Main Card */}\n                  <div className={cn(\n                    \"relative w-full max-w-md mx-auto aspect-[1.6/1] rounded-2xl p-8 transition-all duration-500\",\n                    selectedEffect.id === 'matrix' && \"bg-black/90 border border-accent\",\n                    selectedEffect.id === 'particles' && \"bg-gradient-to-br from-primary/20 to-secondary/20 border border-primary\",\n                    selectedEffect.id === 'tilt' && \"glass border border-white/20\",\n                    selectedEffect.id === 'glow' && \"bg-surface neon-border glow-primary\",\n                    selectedEffect.id === 'hologram' && \"bg-gradient-to-br from-accent/10 to-primary/10 border border-accent\",\n                    !['matrix', 'particles', 'tilt', 'glow', 'hologram'].includes(selectedEffect.id) && cardData.background\n                  )}>\n                    \n                    {/* Effect Overlays */}\n                    {selectedEffect.id === 'matrix' && (\n                      <div className=\"absolute inset-0 overflow-hidden rounded-2xl\">\n                        {[...Array(20)].map((_, i) => (\n                          <div\n                            key={i}\n                            className=\"absolute text-accent text-xs font-mono animate-matrix opacity-60\"\n                            style={{\n                              left: `${Math.random() * 100}%`,\n                              animationDelay: `${Math.random() * 2}s`,\n                              animationDuration: `${3 + Math.random() * 2}s`\n                            }}\n                          >\n                            {Math.random().toString(36).substring(2, 8)}\n                          </div>\n                        ))}\n                      </div>\n                    )}\n\n                    {selectedEffect.id === 'particles' && (\n                      <div className=\"absolute inset-0 overflow-hidden rounded-2xl\">\n                        {[...Array(15)].map((_, i) => (\n                          <div\n                            key={i}\n                            className=\"absolute w-2 h-2 bg-primary rounded-full animate-float opacity-70\"\n                            style={{\n                              left: `${Math.random() * 100}%`,\n                              top: `${Math.random() * 100}%`,\n                              animationDelay: `${Math.random() * 3}s`,\n                              animationDuration: `${2 + Math.random() * 2}s`\n                            }}\n                          />\n                        ))}\n                      </div>\n                    )}\n\n                    {selectedEffect.id === 'hologram' && (\n                      <div className=\"absolute inset-0 rounded-2xl\">\n                        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-accent/20 to-transparent animate-pulse\" />\n                        <div className=\"absolute top-0 left-0 right-0 h-1 bg-accent animate-pulse\" />\n                        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-accent animate-pulse\" />\n                      </div>\n                    )}\n\n                    {/* Card Content */}\n                    <div className=\"relative z-10 h-full flex flex-col justify-between text-center\">\n                      {/* Avatar */}\n                      <div className=\"flex justify-center mb-4\">\n                        <div className=\"w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-xl font-bold text-background\">\n                          {cardData.name.charAt(0)}\n                        </div>\n                      </div>\n                      \n                      {/* Name & Title */}\n                      <div className=\"space-y-2 mb-4\">\n                        <h3 className=\"text-xl font-bold text-gradient\">\n                          {cardData.name}\n                        </h3>\n                        <p className=\"text-text-muted\">{cardData.title}</p>\n                        <p className=\"text-sm text-text-dim\">{cardData.company}</p>\n                      </div>\n                      \n                      {/* Contact Info */}\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex items-center justify-center space-x-2\">\n                          <span>📧</span>\n                          <span className=\"text-text-muted\">{cardData.email}</span>\n                        </div>\n                        <div className=\"flex items-center justify-center space-x-2\">\n                          <span>📱</span>\n                          <span className=\"text-text-muted\">{cardData.phone}</span>\n                        </div>\n                        <div className=\"flex items-center justify-center space-x-2\">\n                          <span>🌐</span>\n                          <span className=\"text-text-muted\">{cardData.website}</span>\n                        </div>\n                      </div>\n                      \n                      {/* AR Effect Indicator */}\n                      <div className=\"pt-4 border-t border-surface-light\">\n                        <div className=\"flex items-center justify-center space-x-2 text-accent\">\n                          <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                          <span className=\"text-xs\">{selectedEffect.name} Active</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {/* Glow Effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl -z-10 animate-pulse-glow\"></div>\n                </div>\n              </div>\n\n              {/* Export Button */}\n              <div className=\"text-center mt-8\">\n                <GradientButton onClick={exportCard} className=\"group\">\n                  <span className=\"mr-2\">📸</span>\n                  Export Card\n                  <svg\n                    className=\"ml-2 w-5 h-5 group-hover:scale-110 transition-transform\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    />\n                  </svg>\n                </GradientButton>\n              </div>\n            </div>\n          </div>\n\n          {/* Customization Panel */}\n          <div className={cn(\n            \"transition-all duration-700\",\n            isVisible ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-10\"\n          )}>\n            <div className=\"space-y-8\">\n              <h3 className=\"text-2xl font-bold text-gradient\">\n                Customize Your Card\n              </h3>\n\n              {/* Personal Information */}\n              <Card variant=\"glass\" className=\"p-6\">\n                <h4 className=\"text-lg font-semibold text-text mb-4\">Personal Information</h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-text-muted mb-2\">Full Name</label>\n                    <input\n                      type=\"text\"\n                      value={cardData.name}\n                      onChange={(e) => handleCardDataChange('name', e.target.value)}\n                      className=\"w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-text-muted mb-2\">Job Title</label>\n                    <input\n                      type=\"text\"\n                      value={cardData.title}\n                      onChange={(e) => handleCardDataChange('title', e.target.value)}\n                      className=\"w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-text-muted mb-2\">Company</label>\n                    <input\n                      type=\"text\"\n                      value={cardData.company}\n                      onChange={(e) => handleCardDataChange('company', e.target.value)}\n                      className=\"w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n              </Card>\n\n              {/* Contact Information */}\n              <Card variant=\"glass\" className=\"p-6\">\n                <h4 className=\"text-lg font-semibold text-text mb-4\">Contact Information</h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-text-muted mb-2\">Email</label>\n                    <input\n                      type=\"email\"\n                      value={cardData.email}\n                      onChange={(e) => handleCardDataChange('email', e.target.value)}\n                      className=\"w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-text-muted mb-2\">Phone</label>\n                    <input\n                      type=\"tel\"\n                      value={cardData.phone}\n                      onChange={(e) => handleCardDataChange('phone', e.target.value)}\n                      className=\"w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-text-muted mb-2\">Website</label>\n                    <input\n                      type=\"url\"\n                      value={cardData.website}\n                      onChange={(e) => handleCardDataChange('website', e.target.value)}\n                      className=\"w-full px-4 py-2 bg-background border border-surface-light rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n              </Card>\n\n              {/* AR Effects */}\n              <Card variant=\"glass\" className=\"p-6\">\n                <h4 className=\"text-lg font-semibold text-text mb-4\">AR Effects</h4>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  {effectTypes.map((effect) => (\n                    <button\n                      key={effect.id}\n                      onClick={() => handleEffectChange(effect)}\n                      className={cn(\n                        \"p-4 rounded-lg border transition-all duration-300 text-left\",\n                        selectedEffect.id === effect.id\n                          ? \"border-primary bg-primary/10 text-primary\"\n                          : \"border-surface-light bg-surface hover:border-primary/50 text-text-muted hover:text-text\"\n                      )}\n                    >\n                      <div className=\"font-medium mb-1\">{effect.name}</div>\n                      <div className=\"text-xs opacity-75\">{effect.description}</div>\n                    </button>\n                  ))}\n                </div>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,qHAAA,CAAA,cAAW,CAAC,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,qHAAA,CAAA,YAAS,CAAC,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,WAAW,IAAI;6CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;4CACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;6CAAO,IAAM,SAAS,UAAU;;QAClC;oCAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC,OAAO;QACnC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,OAAO,EAAE;YACnB,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,gCAAgC;QAChC,MAAM;IACR;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,YAAY,8BAA8B;0CAE1C,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAKlE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+FACA,eAAe,EAAE,KAAK,YAAY,oCAClC,eAAe,EAAE,KAAK,eAAe,2EACrC,eAAe,EAAE,KAAK,UAAU,gCAChC,eAAe,EAAE,KAAK,UAAU,uCAChC,eAAe,EAAE,KAAK,cAAc,uEACpC,CAAC;4DAAC;4DAAU;4DAAa;4DAAQ;4DAAQ;yDAAW,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,SAAS,UAAU;;4DAItG,eAAe,EAAE,KAAK,0BACrB,6LAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM;iEAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wEAEC,WAAU;wEACV,OAAO;4EACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4EAC/B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4EACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wEAChD;kFAEC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;uEARpC;;;;;;;;;;4DAcZ,eAAe,EAAE,KAAK,6BACrB,6LAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM;iEAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wEAEC,WAAU;wEACV,OAAO;4EACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4EAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4EAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4EACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wEAChD;uEAPK;;;;;;;;;;4DAaZ,eAAe,EAAE,KAAK,4BACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;0EAKnB,6LAAC;gEAAI,WAAU;;kFAEb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACZ,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kFAK1B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FACX,SAAS,IAAI;;;;;;0FAEhB,6LAAC;gFAAE,WAAU;0FAAmB,SAAS,KAAK;;;;;;0FAC9C,6LAAC;gFAAE,WAAU;0FAAyB,SAAS,OAAO;;;;;;;;;;;;kFAIxD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;kGAAK;;;;;;kGACN,6LAAC;wFAAK,WAAU;kGAAmB,SAAS,KAAK;;;;;;;;;;;;0FAEnD,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;kGAAK;;;;;;kGACN,6LAAC;wFAAK,WAAU;kGAAmB,SAAS,KAAK;;;;;;;;;;;;0FAEnD,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;kGAAK;;;;;;kGACN,6LAAC;wFAAK,WAAU;kGAAmB,SAAS,OAAO;;;;;;;;;;;;;;;;;;kFAKvD,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAK,WAAU;;wFAAW,eAAe,IAAI;wFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAOvD,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;sDAKnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,iBAAc;gDAAC,SAAS;gDAAY,WAAU;;kEAC7C,6LAAC;wDAAK,WAAU;kEAAO;;;;;;oDAAS;kEAEhC,6LAAC;wDACC,WAAU;wDACV,MAAK;wDACL,QAAO;wDACP,SAAQ;kEAER,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,aAAa;4DACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASd,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,YAAY,8BAA8B;0CAE1C,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAKjD,6LAAC,kIAAA,CAAA,UAAI;4CAAC,SAAQ;4CAAQ,WAAU;;8DAC9B,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiD;;;;;;8EAClE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK;oEAC5D,WAAU;;;;;;;;;;;;sEAId,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiD;;;;;;8EAClE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC7D,WAAU;;;;;;;;;;;;sEAId,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiD;;;;;;8EAClE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,OAAO;oEACvB,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAC/D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAOlB,6LAAC,kIAAA,CAAA,UAAI;4CAAC,SAAQ;4CAAQ,WAAU;;8DAC9B,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiD;;;;;;8EAClE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC7D,WAAU;;;;;;;;;;;;sEAId,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiD;;;;;;8EAClE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC7D,WAAU;;;;;;;;;;;;sEAId,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAiD;;;;;;8EAClE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,OAAO;oEACvB,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAC/D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAOlB,6LAAC,kIAAA,CAAA,UAAI;4CAAC,SAAQ;4CAAQ,WAAU;;8DAC9B,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;8DACZ,qHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,uBAChB,6LAAC;4DAEC,SAAS,IAAM,mBAAmB;4DAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,eAAe,EAAE,KAAK,OAAO,EAAE,GAC3B,8CACA;;8EAGN,6LAAC;oEAAI,WAAU;8EAAoB,OAAO,IAAI;;;;;;8EAC9C,6LAAC;oEAAI,WAAU;8EAAsB,OAAO,WAAW;;;;;;;2DAVlD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GA5TwB;KAAA", "debugId": null}}, {"offset": {"line": 2749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/SharingMethodsDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\n\nexport default function SharingMethodsDemo() {\n  const [activeMethod, setActiveMethod] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const [qrGenerated, setQrGenerated] = useState(false);\n  const [nfcActive, setNfcActive] = useState(false);\n  const [cameraActive, setCameraActive] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const sectionRef = useRef(null);\n\n  const sharingMethods = [\n    {\n      id: 'qr',\n      name: 'QR Code',\n      icon: '📱',\n      description: 'Generate instant QR codes for quick sharing',\n      color: 'primary'\n    },\n    {\n      id: 'nfc',\n      name: 'NFC Tap',\n      icon: '📡',\n      description: 'Near Field Communication for contactless sharing',\n      color: 'secondary'\n    },\n    {\n      id: 'camera',\n      name: 'Camera Scan',\n      icon: '📷',\n      description: 'AI-powered visual recognition technology',\n      color: 'accent'\n    },\n    {\n      id: 'search',\n      name: 'Name Search',\n      icon: '🔍',\n      description: 'Find cards by name or company instantly',\n      color: 'primary'\n    }\n  ];\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const handleMethodClick = (index) => {\n    setActiveMethod(index);\n    // Reset all states\n    setQrGenerated(false);\n    setNfcActive(false);\n    setCameraActive(false);\n    setSearchQuery('');\n  };\n\n  const generateQR = () => {\n    setQrGenerated(true);\n    setTimeout(() => setQrGenerated(false), 3000);\n  };\n\n  const activateNFC = () => {\n    setNfcActive(true);\n    setTimeout(() => setNfcActive(false), 3000);\n  };\n\n  const activateCamera = () => {\n    setCameraActive(true);\n    setTimeout(() => setCameraActive(false), 3000);\n  };\n\n  const handleSearch = (query) => {\n    setSearchQuery(query);\n  };\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-background relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `radial-gradient(circle at 30% 70%, var(--secondary) 0%, transparent 50%), \n                           radial-gradient(circle at 70% 30%, var(--accent) 0%, transparent 50%)`,\n          backgroundSize: '300px 300px'\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">Sharing Methods</span>\n            <br />\n            <span className=\"text-text\">Multiple Ways to Connect</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto\">\n            Experience all the ways you can share your AR business card. \n            From QR codes to AI-powered recognition, we've got you covered.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Method Selection */}\n          <div className={cn(\n            \"space-y-6 transition-all duration-700\",\n            isVisible ? \"opacity-100 translate-x-0\" : \"opacity-0 -translate-x-10\"\n          )}>\n            <h3 className=\"text-2xl font-bold text-gradient mb-6\">\n              Choose a Sharing Method\n            </h3>\n\n            {sharingMethods.map((method, index) => (\n              <button\n                key={method.id}\n                onClick={() => handleMethodClick(index)}\n                className={cn(\n                  \"w-full p-6 rounded-xl border transition-all duration-300 text-left group\",\n                  activeMethod === index\n                    ? \"border-primary bg-primary/10 scale-105\"\n                    : \"border-surface-light bg-surface hover:border-primary/50 hover:scale-102\"\n                )}\n              >\n                <div className=\"flex items-center space-x-4\">\n                  <div className={cn(\n                    \"text-4xl p-3 rounded-lg transition-all duration-300\",\n                    activeMethod === index\n                      ? \"bg-gradient-to-r from-primary to-secondary\"\n                      : \"bg-surface-light group-hover:bg-primary/20\"\n                  )}>\n                    {method.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className={cn(\n                      \"text-xl font-semibold mb-2 transition-colors duration-300\",\n                      activeMethod === index ? \"text-gradient\" : \"text-text\"\n                    )}>\n                      {method.name}\n                    </h4>\n                    <p className=\"text-text-muted\">\n                      {method.description}\n                    </p>\n                  </div>\n                  <div className={cn(\n                    \"w-4 h-4 rounded-full transition-all duration-300\",\n                    activeMethod === index \n                      ? \"bg-primary animate-pulse\" \n                      : \"bg-surface-light\"\n                  )} />\n                </div>\n              </button>\n            ))}\n          </div>\n\n          {/* Demo Area */}\n          <div className={cn(\n            \"transition-all duration-700\",\n            isVisible ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-10\"\n          )}>\n            <Card variant=\"glass\" className=\"p-8 min-h-[500px] flex flex-col justify-center\">\n              {/* QR Code Demo */}\n              {activeMethod === 0 && (\n                <div className=\"text-center space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">QR Code Generator</h4>\n                  \n                  <div className=\"relative\">\n                    <div className={cn(\n                      \"w-48 h-48 mx-auto border-2 border-dashed rounded-lg flex items-center justify-center transition-all duration-500\",\n                      qrGenerated \n                        ? \"border-primary bg-primary/10\" \n                        : \"border-surface-light bg-surface\"\n                    )}>\n                      {qrGenerated ? (\n                        <div className=\"space-y-4\">\n                          <div className=\"grid grid-cols-8 gap-1\">\n                            {[...Array(64)].map((_, i) => (\n                              <div\n                                key={i}\n                                className={cn(\n                                  \"w-2 h-2 rounded-sm\",\n                                  Math.random() > 0.5 ? \"bg-primary\" : \"bg-transparent\"\n                                )}\n                              />\n                            ))}\n                          </div>\n                          <div className=\"text-sm text-primary font-medium\">QR Code Generated!</div>\n                        </div>\n                      ) : (\n                        <div className=\"text-text-muted\">\n                          <div className=\"text-4xl mb-2\">📱</div>\n                          <div>Click to generate</div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <Button \n                    variant=\"gradient\" \n                    onClick={generateQR}\n                    disabled={qrGenerated}\n                  >\n                    {qrGenerated ? 'Generated!' : 'Generate QR Code'}\n                  </Button>\n                  \n                  <p className=\"text-sm text-text-muted\">\n                    Scan with any smartphone camera to view the AR card\n                  </p>\n                </div>\n              )}\n\n              {/* NFC Demo */}\n              {activeMethod === 1 && (\n                <div className=\"text-center space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">NFC Sharing</h4>\n                  \n                  <div className=\"relative\">\n                    <div className={cn(\n                      \"w-32 h-32 mx-auto rounded-full border-4 flex items-center justify-center transition-all duration-500\",\n                      nfcActive \n                        ? \"border-secondary bg-secondary/20 animate-pulse-glow\" \n                        : \"border-surface-light bg-surface\"\n                    )}>\n                      <div className=\"text-4xl\">📡</div>\n                    </div>\n                    \n                    {nfcActive && (\n                      <div className=\"absolute inset-0 flex items-center justify-center\">\n                        <div className=\"w-40 h-40 border-2 border-secondary rounded-full animate-ping opacity-75\"></div>\n                      </div>\n                    )}\n                  </div>\n                  \n                  <Button \n                    variant=\"gradient\" \n                    onClick={activateNFC}\n                    disabled={nfcActive}\n                  >\n                    {nfcActive ? 'NFC Active!' : 'Activate NFC'}\n                  </Button>\n                  \n                  <p className=\"text-sm text-text-muted\">\n                    Tap NFC-enabled devices to instantly share your card\n                  </p>\n                </div>\n              )}\n\n              {/* Camera Scan Demo */}\n              {activeMethod === 2 && (\n                <div className=\"text-center space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">Camera Recognition</h4>\n                  \n                  <div className=\"relative\">\n                    <div className={cn(\n                      \"w-48 h-36 mx-auto border-2 rounded-lg flex items-center justify-center transition-all duration-500\",\n                      cameraActive \n                        ? \"border-accent bg-accent/10\" \n                        : \"border-surface-light bg-surface\"\n                    )}>\n                      {cameraActive ? (\n                        <div className=\"space-y-2\">\n                          <div className=\"text-4xl animate-pulse\">👤</div>\n                          <div className=\"text-sm text-accent\">Face Detected!</div>\n                          <div className=\"w-24 h-1 bg-accent rounded-full animate-pulse\"></div>\n                        </div>\n                      ) : (\n                        <div className=\"text-text-muted\">\n                          <div className=\"text-4xl mb-2\">📷</div>\n                          <div>Camera viewfinder</div>\n                        </div>\n                      )}\n                    </div>\n                    \n                    {cameraActive && (\n                      <div className=\"absolute inset-0 border-2 border-accent rounded-lg animate-pulse\"></div>\n                    )}\n                  </div>\n                  \n                  <Button \n                    variant=\"gradient\" \n                    onClick={activateCamera}\n                    disabled={cameraActive}\n                  >\n                    {cameraActive ? 'Scanning...' : 'Start Camera Scan'}\n                  </Button>\n                  \n                  <p className=\"text-sm text-text-muted\">\n                    AI recognizes faces and displays associated business cards\n                  </p>\n                </div>\n              )}\n\n              {/* Name Search Demo */}\n              {activeMethod === 3 && (\n                <div className=\"text-center space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">Name Search</h4>\n                  \n                  <div className=\"space-y-4\">\n                    <div className=\"relative\">\n                      <input\n                        type=\"text\"\n                        placeholder=\"Search by name or company...\"\n                        value={searchQuery}\n                        onChange={(e) => handleSearch(e.target.value)}\n                        className=\"w-full px-4 py-3 bg-background border border-surface-light rounded-lg text-text placeholder-text-dim focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                      />\n                      <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted\">\n                        🔍\n                      </div>\n                    </div>\n                    \n                    {searchQuery && (\n                      <div className=\"space-y-2 text-left\">\n                        <div className=\"p-3 bg-surface rounded-lg border border-primary/30 hover:border-primary/50 transition-colors cursor-pointer\">\n                          <div className=\"flex items-center space-x-3\">\n                            <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-sm font-bold text-background\">\n                              A\n                            </div>\n                            <div>\n                              <div className=\"font-medium text-text\">Alex Thompson</div>\n                              <div className=\"text-sm text-text-muted\">Senior Developer at TechFlow</div>\n                            </div>\n                          </div>\n                        </div>\n                        \n                        <div className=\"p-3 bg-surface rounded-lg border border-surface-light hover:border-primary/30 transition-colors cursor-pointer\">\n                          <div className=\"flex items-center space-x-3\">\n                            <div className=\"w-8 h-8 bg-gradient-to-r from-secondary to-accent rounded-full flex items-center justify-center text-sm font-bold text-background\">\n                              M\n                            </div>\n                            <div>\n                              <div className=\"font-medium text-text\">Maria Garcia</div>\n                              <div className=\"text-sm text-text-muted\">UX Designer at Creative Studio</div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  \n                  <p className=\"text-sm text-text-muted\">\n                    Find any card instantly by typing a name or company\n                  </p>\n                </div>\n              )}\n            </Card>\n          </div>\n        </div>\n\n        {/* Features Comparison */}\n        <div className=\"mt-20\">\n          <Card variant=\"glass\" className=\"p-8\">\n            <h3 className=\"text-2xl font-bold text-gradient text-center mb-8\">\n              Why Multiple Sharing Methods Matter\n            </h3>\n            \n            <div className=\"grid md:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">⚡</div>\n                <h4 className=\"font-semibold text-text mb-2\">Instant Access</h4>\n                <p className=\"text-sm text-text-muted\">No app downloads or account creation required</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">🌐</div>\n                <h4 className=\"font-semibold text-text mb-2\">Universal Compatibility</h4>\n                <p className=\"text-sm text-text-muted\">Works on any device with a web browser</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">🔒</div>\n                <h4 className=\"font-semibold text-text mb-2\">Privacy Focused</h4>\n                <p className=\"text-sm text-text-muted\">You control who sees your card and when</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">📊</div>\n                <h4 className=\"font-semibold text-text mb-2\">Analytics Included</h4>\n                <p className=\"text-sm text-text-muted\">Track views and engagement in real-time</p>\n              </div>\n            </div>\n          </Card>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;QACT;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,WAAW,IAAI;gDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;+CACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;gDAAO,IAAM,SAAS,UAAU;;QAClC;uCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,mBAAmB;QACnB,eAAe;QACf,aAAa;QACb,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,WAAW,IAAM,aAAa,QAAQ;IACxC;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,WAAW,IAAM,gBAAgB,QAAQ;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;gGACoE,CAAC;wBACvF,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,yCACA,YAAY,8BAA8B;;kDAE1C,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;oCAIrD,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC;4CAEC,SAAS,IAAM,kBAAkB;4CACjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA,iBAAiB,QACb,2CACA;sDAGN,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uDACA,iBAAiB,QACb,+CACA;kEAEH,OAAO,IAAI;;;;;;kEAEd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,6DACA,iBAAiB,QAAQ,kBAAkB;0EAE1C,OAAO,IAAI;;;;;;0EAEd,6LAAC;gEAAE,WAAU;0EACV,OAAO,WAAW;;;;;;;;;;;;kEAGvB,6LAAC;wDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oDACA,iBAAiB,QACb,6BACA;;;;;;;;;;;;2CAjCH,OAAO,EAAE;;;;;;;;;;;0CAyCpB,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,YAAY,8BAA8B;0CAE1C,cAAA,6LAAC,kIAAA,CAAA,UAAI;oCAAC,SAAQ;oCAAQ,WAAU;;wCAE7B,iBAAiB,mBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oHACA,cACI,iCACA;kEAEH,4BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ;2EAAI,MAAM;qEAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;4EAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,KAAK,MAAM,KAAK,MAAM,eAAe;2EAHlC;;;;;;;;;;8EAQX,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;iFAGpD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,6LAAC;8EAAI;;;;;;;;;;;;;;;;;;;;;;8DAMb,6LAAC,oIAAA,CAAA,UAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,UAAU;8DAET,cAAc,eAAe;;;;;;8DAGhC,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;wCAO1C,iBAAiB,mBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wGACA,YACI,wDACA;sEAEJ,cAAA,6LAAC;gEAAI,WAAU;0EAAW;;;;;;;;;;;wDAG3B,2BACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;8DAKrB,6LAAC,oIAAA,CAAA,UAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,UAAU;8DAET,YAAY,gBAAgB;;;;;;8DAG/B,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;wCAO1C,iBAAiB,mBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,sGACA,eACI,+BACA;sEAEH,6BACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;kFAAsB;;;;;;kFACrC,6LAAC;wEAAI,WAAU;;;;;;;;;;;qFAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAC/B,6LAAC;kFAAI;;;;;;;;;;;;;;;;;wDAKV,8BACC,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAInB,6LAAC,oIAAA,CAAA,UAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,UAAU;8DAET,eAAe,gBAAgB;;;;;;8DAGlC,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;wCAO1C,iBAAiB,mBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oEAC5C,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;8EAAsE;;;;;;;;;;;;wDAKtF,6BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAAqI;;;;;;0FAGpJ,6LAAC;;kGACC,6LAAC;wFAAI,WAAU;kGAAwB;;;;;;kGACvC,6LAAC;wFAAI,WAAU;kGAA0B;;;;;;;;;;;;;;;;;;;;;;;8EAK/C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAAoI;;;;;;0FAGnJ,6LAAC;;kGACC,6LAAC;wFAAI,WAAU;kGAAwB;;;;;;kGACvC,6LAAC;wFAAI,WAAU;kGAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAQrD,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUjD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kIAAA,CAAA,UAAI;4BAAC,SAAQ;4BAAQ,WAAU;;8CAC9B,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAIlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GAzYwB;KAAA", "debugId": null}}, {"offset": {"line": 3668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/InteractiveFeaturesDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport Button, { GradientButton } from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\n\nexport default function InteractiveFeaturesDemo() {\n  const [activeDemo, setActiveDemo] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });\n  const [selectedColor, setSelectedColor] = useState('#00f5ff');\n  const [animationSpeed, setAnimationSpeed] = useState(1);\n  const [savedCards, setSavedCards] = useState([]);\n  const sectionRef = useRef(null);\n\n  const demos = [\n    {\n      id: 'drag-drop',\n      name: 'Drag & Drop Customization',\n      icon: '🎨',\n      description: 'Intuitive drag and drop interface for easy customization'\n    },\n    {\n      id: 'color-picker',\n      name: 'Live Color Updates',\n      icon: '🌈',\n      description: 'Real-time color changes with instant preview'\n    },\n    {\n      id: 'animation-controls',\n      name: 'Animation Timeline',\n      icon: '⏯️',\n      description: 'Control animation speed and timing'\n    },\n    {\n      id: 'save-load',\n      name: 'Save & Load System',\n      icon: '💾',\n      description: 'Save your designs and load them anytime'\n    }\n  ];\n\n  const colors = [\n    '#00f5ff', '#ff0080', '#00ff41', '#ff6b35', '#7b68ee', \n    '#ff1493', '#00ced1', '#ffd700', '#ff4500', '#9370db'\n  ];\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const handleDrag = (e) => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    const x = ((e.clientX - rect.left) / rect.width) * 100;\n    const y = ((e.clientY - rect.top) / rect.height) * 100;\n    setDragPosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });\n  };\n\n  const saveCard = () => {\n    const newCard = {\n      id: Date.now(),\n      name: `Design ${savedCards.length + 1}`,\n      color: selectedColor,\n      position: dragPosition,\n      speed: animationSpeed,\n      timestamp: new Date().toLocaleTimeString()\n    };\n    setSavedCards(prev => [...prev, newCard]);\n  };\n\n  const loadCard = (card) => {\n    setSelectedColor(card.color);\n    setDragPosition(card.position);\n    setAnimationSpeed(card.speed);\n  };\n\n  const deleteCard = (cardId) => {\n    setSavedCards(prev => prev.filter(card => card.id !== cardId));\n  };\n\n  return (\n    <section ref={sectionRef} className=\"py-20 bg-gradient-to-b from-surface to-background relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `linear-gradient(45deg, var(--primary) 0%, transparent 25%), \n                           linear-gradient(-45deg, var(--secondary) 0%, transparent 25%)`,\n          backgroundSize: '100px 100px'\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-6\">\n            <span className=\"text-gradient\">Interactive Features</span>\n            <br />\n            <span className=\"text-text\">Hands-On Experience</span>\n          </h2>\n          <p className=\"text-xl text-text-muted max-w-3xl mx-auto\">\n            Try out the powerful customization tools that make NameCardAI \n            the most user-friendly AR business card platform.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Demo Selection */}\n          <div className={cn(\n            \"space-y-4 transition-all duration-700\",\n            isVisible ? \"opacity-100 translate-x-0\" : \"opacity-0 -translate-x-10\"\n          )}>\n            <h3 className=\"text-xl font-bold text-gradient mb-6\">\n              Choose a Feature to Try\n            </h3>\n\n            {demos.map((demo, index) => (\n              <button\n                key={demo.id}\n                onClick={() => setActiveDemo(index)}\n                className={cn(\n                  \"w-full p-4 rounded-lg border transition-all duration-300 text-left\",\n                  activeDemo === index\n                    ? \"border-primary bg-primary/10 scale-105\"\n                    : \"border-surface-light bg-surface hover:border-primary/50\"\n                )}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"text-2xl\">{demo.icon}</div>\n                  <div>\n                    <div className={cn(\n                      \"font-medium mb-1\",\n                      activeDemo === index ? \"text-gradient\" : \"text-text\"\n                    )}>\n                      {demo.name}\n                    </div>\n                    <div className=\"text-sm text-text-muted\">\n                      {demo.description}\n                    </div>\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n\n          {/* Demo Area */}\n          <div className={cn(\n            \"lg:col-span-2 transition-all duration-700\",\n            isVisible ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-10\"\n          )}>\n            <Card variant=\"glass\" className=\"p-8 min-h-[600px]\">\n              {/* Drag & Drop Demo */}\n              {activeDemo === 0 && (\n                <div className=\"space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">Drag & Drop Interface</h4>\n                  \n                  <div \n                    className=\"relative w-full h-80 bg-gradient-to-br from-background to-surface border-2 border-dashed border-primary/30 rounded-lg cursor-crosshair overflow-hidden\"\n                    onMouseMove={handleDrag}\n                  >\n                    <div \n                      className=\"absolute w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-background font-bold text-xl transition-all duration-200 animate-pulse-glow\"\n                      style={{\n                        left: `${dragPosition.x}%`,\n                        top: `${dragPosition.y}%`,\n                        transform: 'translate(-50%, -50%)'\n                      }}\n                    >\n                      AI\n                    </div>\n                    \n                    <div className=\"absolute bottom-4 left-4 text-sm text-text-muted\">\n                      Move your mouse to position the avatar\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-center\">\n                    <p className=\"text-text-muted\">\n                      Position: X: {Math.round(dragPosition.x)}%, Y: {Math.round(dragPosition.y)}%\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Color Picker Demo */}\n              {activeDemo === 1 && (\n                <div className=\"space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">Live Color Customization</h4>\n                  \n                  <div className=\"text-center\">\n                    <div \n                      className=\"w-32 h-32 mx-auto rounded-full flex items-center justify-center text-white font-bold text-2xl transition-all duration-300 animate-pulse-glow\"\n                      style={{ backgroundColor: selectedColor }}\n                    >\n                      AI\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-4\">\n                    <h5 className=\"font-semibold text-text\">Choose a Color:</h5>\n                    <div className=\"grid grid-cols-5 gap-3\">\n                      {colors.map((color) => (\n                        <button\n                          key={color}\n                          onClick={() => setSelectedColor(color)}\n                          className={cn(\n                            \"w-12 h-12 rounded-full border-2 transition-all duration-300 hover:scale-110\",\n                            selectedColor === color \n                              ? \"border-white shadow-lg scale-110\" \n                              : \"border-transparent\"\n                          )}\n                          style={{ backgroundColor: color }}\n                        />\n                      ))}\n                    </div>\n                    \n                    <div className=\"mt-4\">\n                      <input\n                        type=\"color\"\n                        value={selectedColor}\n                        onChange={(e) => setSelectedColor(e.target.value)}\n                        className=\"w-full h-12 rounded-lg border border-surface-light cursor-pointer\"\n                      />\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-center text-sm text-text-muted\">\n                    Selected Color: {selectedColor}\n                  </div>\n                </div>\n              )}\n\n              {/* Animation Controls Demo */}\n              {activeDemo === 2 && (\n                <div className=\"space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">Animation Timeline Controls</h4>\n                  \n                  <div className=\"text-center\">\n                    <div \n                      className=\"w-32 h-32 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-background font-bold text-2xl\"\n                      style={{\n                        animation: `float ${3 / animationSpeed}s ease-in-out infinite`\n                      }}\n                    >\n                      AI\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-4\">\n                    <h5 className=\"font-semibold text-text\">Animation Speed:</h5>\n                    <div className=\"space-y-2\">\n                      <input\n                        type=\"range\"\n                        min=\"0.1\"\n                        max=\"3\"\n                        step=\"0.1\"\n                        value={animationSpeed}\n                        onChange={(e) => setAnimationSpeed(parseFloat(e.target.value))}\n                        className=\"w-full h-2 bg-surface rounded-lg appearance-none cursor-pointer\"\n                      />\n                      <div className=\"flex justify-between text-sm text-text-muted\">\n                        <span>Slow</span>\n                        <span>Speed: {animationSpeed}x</span>\n                        <span>Fast</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-3 gap-3 mt-6\">\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => setAnimationSpeed(0.5)}\n                      >\n                        Slow\n                      </Button>\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => setAnimationSpeed(1)}\n                      >\n                        Normal\n                      </Button>\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => setAnimationSpeed(2)}\n                      >\n                        Fast\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Save & Load Demo */}\n              {activeDemo === 3 && (\n                <div className=\"space-y-6\">\n                  <h4 className=\"text-2xl font-bold text-gradient\">Save & Load System</h4>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    {/* Current Design */}\n                    <div className=\"space-y-4\">\n                      <h5 className=\"font-semibold text-text\">Current Design:</h5>\n                      <div className=\"p-4 bg-surface rounded-lg border border-surface-light\">\n                        <div className=\"text-center mb-4\">\n                          <div \n                            className=\"w-16 h-16 mx-auto rounded-full flex items-center justify-center text-white font-bold\"\n                            style={{ backgroundColor: selectedColor }}\n                          >\n                            AI\n                          </div>\n                        </div>\n                        <div className=\"text-sm text-text-muted space-y-1\">\n                          <div>Color: {selectedColor}</div>\n                          <div>Position: {Math.round(dragPosition.x)}%, {Math.round(dragPosition.y)}%</div>\n                          <div>Speed: {animationSpeed}x</div>\n                        </div>\n                      </div>\n                      \n                      <GradientButton onClick={saveCard} className=\"w-full\">\n                        <span className=\"mr-2\">💾</span>\n                        Save Design\n                      </GradientButton>\n                    </div>\n                    \n                    {/* Saved Designs */}\n                    <div className=\"space-y-4\">\n                      <h5 className=\"font-semibold text-text\">Saved Designs ({savedCards.length}):</h5>\n                      <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                        {savedCards.length === 0 ? (\n                          <div className=\"text-center text-text-muted py-8\">\n                            No saved designs yet\n                          </div>\n                        ) : (\n                          savedCards.map((card) => (\n                            <div key={card.id} className=\"p-3 bg-surface rounded-lg border border-surface-light\">\n                              <div className=\"flex items-center justify-between\">\n                                <div className=\"flex items-center space-x-3\">\n                                  <div \n                                    className=\"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold\"\n                                    style={{ backgroundColor: card.color }}\n                                  >\n                                    AI\n                                  </div>\n                                  <div>\n                                    <div className=\"font-medium text-text\">{card.name}</div>\n                                    <div className=\"text-xs text-text-muted\">{card.timestamp}</div>\n                                  </div>\n                                </div>\n                                <div className=\"flex space-x-2\">\n                                  <button\n                                    onClick={() => loadCard(card)}\n                                    className=\"text-primary hover:text-primary/80 text-sm\"\n                                  >\n                                    Load\n                                  </button>\n                                  <button\n                                    onClick={() => deleteCard(card.id)}\n                                    className=\"text-red-400 hover:text-red-300 text-sm\"\n                                  >\n                                    Delete\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n                          ))\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </Card>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-16 text-center\">\n          <Card variant=\"glass\" className=\"p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gradient mb-4\">\n              Ready to Create Your AR Business Card?\n            </h3>\n            <p className=\"text-text-muted mb-8\">\n              Experience all these features and more with your free NameCardAI account. \n              Start building your professional digital identity today.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <GradientButton size=\"lg\">\n                <span className=\"mr-2\">🚀</span>\n                Start Creating Free\n              </GradientButton>\n              <Button variant=\"outline\" size=\"lg\">\n                <span className=\"mr-2\">📖</span>\n                View Documentation\n              </Button>\n            </div>\n          </Card>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,SAAS;QACb;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,WAAW,IAAI;qDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;oDACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;qDAAO,IAAM,SAAS,UAAU;;QAClC;4CAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,IAAI,AAAC,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAI;QACnD,MAAM,IAAI,AAAC,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM,GAAI;QACnD,gBAAgB;YAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;YAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QAAI;IACvF;IAEA,MAAM,WAAW;QACf,MAAM,UAAU;YACd,IAAI,KAAK,GAAG;YACZ,MAAM,CAAC,OAAO,EAAE,WAAW,MAAM,GAAG,GAAG;YACvC,OAAO;YACP,UAAU;YACV,OAAO;YACP,WAAW,IAAI,OAAO,kBAAkB;QAC1C;QACA,cAAc,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IAC1C;IAEA,MAAM,WAAW,CAAC;QAChB,iBAAiB,KAAK,KAAK;QAC3B,gBAAgB,KAAK,QAAQ;QAC7B,kBAAkB,KAAK,KAAK;IAC9B;IAEA,MAAM,aAAa,CAAC;QAClB,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxD;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;wFAC4D,CAAC;wBAC/E,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,yCACA,YAAY,8BAA8B;;kDAE1C,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;oCAIpD,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4CAEC,SAAS,IAAM,cAAc;4CAC7B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA,eAAe,QACX,2CACA;sDAGN,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAY,KAAK,IAAI;;;;;;kEACpC,6LAAC;;0EACC,6LAAC;gEAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oBACA,eAAe,QAAQ,kBAAkB;0EAExC,KAAK,IAAI;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;0EACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;2CAnBlB,KAAK,EAAE;;;;;;;;;;;0CA4BlB,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6CACA,YAAY,8BAA8B;0CAE1C,cAAA,6LAAC,kIAAA,CAAA,UAAI;oCAAC,SAAQ;oCAAQ,WAAU;;wCAE7B,eAAe,mBACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDACC,WAAU;oDACV,aAAa;;sEAEb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gEAC1B,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gEACzB,WAAW;4DACb;sEACD;;;;;;sEAID,6LAAC;4DAAI,WAAU;sEAAmD;;;;;;;;;;;;8DAKpE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;4DAAkB;4DACf,KAAK,KAAK,CAAC,aAAa,CAAC;4DAAE;4DAAO,KAAK,KAAK,CAAC,aAAa,CAAC;4DAAE;;;;;;;;;;;;;;;;;;wCAOlF,eAAe,mBACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAc;kEACzC;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0B;;;;;;sEACxC,6LAAC;4DAAI,WAAU;sEACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;oEAEC,SAAS,IAAM,iBAAiB;oEAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA,kBAAkB,QACd,qCACA;oEAEN,OAAO;wEAAE,iBAAiB;oEAAM;mEAR3B;;;;;;;;;;sEAaX,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,WAAU;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;wDAAsC;wDAClC;;;;;;;;;;;;;wCAMtB,eAAe,mBACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,WAAW,CAAC,MAAM,EAAE,IAAI,eAAe,sBAAsB,CAAC;wDAChE;kEACD;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0B;;;;;;sEACxC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,KAAI;oEACJ,KAAI;oEACJ,MAAK;oEACL,OAAO;oEACP,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAC5D,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;;gFAAK;gFAAQ;gFAAe;;;;;;;sFAC7B,6LAAC;sFAAK;;;;;;;;;;;;;;;;;;sEAIV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,UAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB;8EAClC;;;;;;8EAGD,6LAAC,oIAAA,CAAA,UAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB;8EAClC;;;;;;8EAGD,6LAAC,oIAAA,CAAA,UAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB;8EAClC;;;;;;;;;;;;;;;;;;;;;;;;wCASR,eAAe,mBACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA0B;;;;;;8EACxC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,iBAAiB;gFAAc;0FACzC;;;;;;;;;;;sFAIH,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;;wFAAI;wFAAQ;;;;;;;8FACb,6LAAC;;wFAAI;wFAAW,KAAK,KAAK,CAAC,aAAa,CAAC;wFAAE;wFAAI,KAAK,KAAK,CAAC,aAAa,CAAC;wFAAE;;;;;;;8FAC1E,6LAAC;;wFAAI;wFAAQ;wFAAe;;;;;;;;;;;;;;;;;;;8EAIhC,6LAAC,oIAAA,CAAA,iBAAc;oEAAC,SAAS;oEAAU,WAAU;;sFAC3C,6LAAC;4EAAK,WAAU;sFAAO;;;;;;wEAAS;;;;;;;;;;;;;sEAMpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;;wEAA0B;wEAAgB,WAAW,MAAM;wEAAC;;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;8EACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;wEAAI,WAAU;kFAAmC;;;;;+EAIlD,WAAW,GAAG,CAAC,CAAC,qBACd,6LAAC;4EAAkB,WAAU;sFAC3B,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGACC,WAAU;gGACV,OAAO;oGAAE,iBAAiB,KAAK,KAAK;gGAAC;0GACtC;;;;;;0GAGD,6LAAC;;kHACC,6LAAC;wGAAI,WAAU;kHAAyB,KAAK,IAAI;;;;;;kHACjD,6LAAC;wGAAI,WAAU;kHAA2B,KAAK,SAAS;;;;;;;;;;;;;;;;;;kGAG5D,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGACC,SAAS,IAAM,SAAS;gGACxB,WAAU;0GACX;;;;;;0GAGD,6LAAC;gGACC,SAAS,IAAM,WAAW,KAAK,EAAE;gGACjC,WAAU;0GACX;;;;;;;;;;;;;;;;;;2EAxBG,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA0CrC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kIAAA,CAAA,UAAI;4BAAC,SAAQ;4BAAQ,WAAU;;8CAC9B,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAuB;;;;;;8CAKpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,iBAAc;4CAAC,MAAK;;8DACnB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,6LAAC,oIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GAxZwB;KAAA", "debugId": null}}, {"offset": {"line": 4621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Why Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Community', href: '/community' },\n      { name: 'Templates', href: '/templates' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', href: '#', icon: '🐦' },\n    { name: 'LinkedIn', href: '#', icon: '💼' },\n    { name: 'GitHub', href: '#', icon: '🐙' },\n    { name: 'Discord', href: '#', icon: '💬' },\n    { name: 'YouTube', href: '#', icon: '📺' }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-surface-light relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `radial-gradient(circle at 20% 80%, var(--primary) 0%, transparent 50%), \n                           radial-gradient(circle at 80% 20%, var(--secondary) 0%, transparent 50%)`,\n          backgroundSize: '200px 200px'\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-5 gap-12\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              {/* Logo */}\n              <Link href=\"/\" className=\"flex items-center space-x-3 group mb-6\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300\">\n                  <svg\n                    className=\"w-8 h-8 text-background\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n                  </svg>\n                </div>\n                <span className=\"text-2xl font-bold font-display text-gradient\">\n                  NameCardAI\n                </span>\n              </Link>\n\n              {/* Description */}\n              <p className=\"text-text-muted mb-6 leading-relaxed\">\n                Revolutionizing professional networking with AR-enhanced digital business cards. \n                Share stunning, interactive profiles that work without apps and create lasting impressions.\n              </p>\n\n              {/* Newsletter Signup */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-text\">Stay Updated</h4>\n                <div className=\"flex space-x-3\">\n                  <input\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"flex-1 px-4 py-2 bg-background border border-surface-light rounded-lg text-text placeholder-text-dim focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\n                  />\n                  <Button variant=\"gradient\" size=\"sm\">\n                    Subscribe\n                  </Button>\n                </div>\n                <p className=\"text-xs text-text-dim\">\n                  Get the latest updates on new features and releases.\n                </p>\n              </div>\n            </div>\n\n            {/* Links Sections */}\n            <div className=\"lg:col-span-3 grid sm:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {/* Product */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Product</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.product.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Company */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Company</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.company.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Resources */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Resources</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.resources.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Legal */}\n              <div>\n                <h4 className=\"font-semibold text-text mb-4\">Legal</h4>\n                <ul className=\"space-y-3\">\n                  {footerLinks.legal.map((link) => (\n                    <li key={link.name}>\n                      <Link\n                        href={link.href}\n                        className=\"text-text-muted hover:text-primary transition-colors duration-300\"\n                      >\n                        {link.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"py-8 border-t border-surface-light\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-text-muted text-sm\">\n              © {currentYear} NameCardAI. All rights reserved. Built with ❤️ for the future of networking.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-6\">\n              <span className=\"text-text-muted text-sm\">Follow us:</span>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social) => (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"text-text-muted hover:text-primary transition-colors duration-300 hover:scale-110 transform\"\n                    title={social.name}\n                  >\n                    <span className=\"text-xl\">{social.icon}</span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"py-6 border-t border-surface-light\">\n          <div className=\"grid md:grid-cols-3 gap-6 text-center md:text-left\">\n            <div>\n              <h5 className=\"font-semibold text-text mb-2\">🌍 Global Reach</h5>\n              <p className=\"text-text-muted text-sm\">\n                Available in 45+ countries with 24/7 support\n              </p>\n            </div>\n            \n            <div>\n              <h5 className=\"font-semibold text-text mb-2\">🔒 Enterprise Security</h5>\n              <p className=\"text-text-muted text-sm\">\n                SOC 2 compliant with end-to-end encryption\n              </p>\n            </div>\n            \n            <div>\n              <h5 className=\"font-semibold text-text mb-2\">⚡ 99.9% Uptime</h5>\n              <p className=\"text-text-muted text-sm\">\n                Reliable infrastructure with global CDN\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Action Button */}\n      <div className=\"fixed bottom-8 right-8 z-50\">\n        <Button\n          variant=\"gradient\"\n          size=\"lg\"\n          className=\"rounded-full w-16 h-16 shadow-2xl animate-pulse-glow\"\n          title=\"Get Help\"\n        >\n          <span className=\"text-2xl\">💬</span>\n        </Button>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;SACzC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAK;QACzC;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM;QAAK;QAC1C;YAAE,MAAM;YAAU,MAAM;YAAK,MAAM;QAAK;QACxC;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAK;QACzC;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM;QAAK;KAC1C;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;mGACuE,CAAC;wBAC1F,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,MAAK;wDACL,SAAQ;kEAER,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAK,WAAU;8DAAgD;;;;;;;;;;;;sDAMlE,6LAAC;4CAAE,WAAU;sDAAuC;;;;;;sDAMpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC,oIAAA,CAAA,UAAM;4DAAC,SAAQ;4DAAW,MAAK;sEAAK;;;;;;;;;;;;8DAIvC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAOzC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;sEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgB9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;wCAA0B;wCACpC;wCAAY;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,OAAO,OAAO,IAAI;8DAElB,cAAA,6LAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;mDALjC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAc5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;8CAKzC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;8CAKzC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,UAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAM;8BAEN,cAAA,6LAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;;;;;;;;;;;AAKrC;KAhPwB", "debugId": null}}]}