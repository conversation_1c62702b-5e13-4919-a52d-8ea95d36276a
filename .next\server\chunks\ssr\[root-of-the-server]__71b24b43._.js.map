{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Utility function to merge Tailwind CSS classes\n * @param {...string} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generate a random number between min and max\n * @param {number} min - Minimum value\n * @param {number} max - Maximum value\n * @returns {number} Random number\n */\nexport function randomBetween(min, max) {\n  return Math.random() * (max - min) + min;\n}\n\n/**\n * Debounce function to limit function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Time limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n/**\n * Format number with commas\n * @param {number} num - Number to format\n * @returns {string} Formatted number\n */\nexport function formatNumber(num) {\n  return new Intl.NumberFormat().format(num);\n}\n\n/**\n * Generate a unique ID\n * @returns {string} Unique ID\n */\nexport function generateId() {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * Check if device is mobile\n * @returns {boolean} True if mobile\n */\nexport function isMobile() {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n}\n\n/**\n * Check if device supports touch\n * @returns {boolean} True if touch supported\n */\nexport function isTouchDevice() {\n  if (typeof window === 'undefined') return false;\n  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n}\n\n/**\n * Smooth scroll to element\n * @param {string} elementId - ID of element to scroll to\n * @param {number} offset - Offset from top\n */\nexport function scrollToElement(elementId, offset = 0) {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Copy text to clipboard\n * @param {string} text - Text to copy\n * @returns {Promise<boolean>} Success status\n */\nexport async function copyToClipboard(text) {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (err) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n/**\n * Get random item from array\n * @param {Array} array - Array to pick from\n * @returns {*} Random item\n */\nexport function getRandomItem(array) {\n  return array[Math.floor(Math.random() * array.length)];\n}\n\n/**\n * Shuffle array\n * @param {Array} array - Array to shuffle\n * @returns {Array} Shuffled array\n */\nexport function shuffleArray(array) {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * Convert hex color to RGB\n * @param {string} hex - Hex color\n * @returns {object} RGB values\n */\nexport function hexToRgb(hex) {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n}\n\n/**\n * Validate email address\n * @param {string} email - Email to validate\n * @returns {boolean} Valid email\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Format date\n * @param {Date|string} date - Date to format\n * @param {string} locale - Locale for formatting\n * @returns {string} Formatted date\n */\nexport function formatDate(date, locale = 'en-US') {\n  return new Intl.DateTimeFormat(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(new Date(date));\n}\n\n/**\n * Calculate reading time\n * @param {string} text - Text to calculate reading time for\n * @param {number} wpm - Words per minute (default: 200)\n * @returns {number} Reading time in minutes\n */\nexport function calculateReadingTime(text, wpm = 200) {\n  const words = text.trim().split(/\\s+/).length;\n  return Math.ceil(words / wpm);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,cAAc,GAAG,EAAE,GAAG;IACpC,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;AACvC;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAOO,SAAS,aAAa,GAAG;IAC9B,OAAO,IAAI,KAAK,YAAY,GAAG,MAAM,CAAC;AACxC;AAMO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAMO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAMO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAOO,SAAS,gBAAgB,SAAS,EAAE,SAAS,CAAC;IACnD,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAOO,eAAe,gBAAgB,IAAI;IACxC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAOO,SAAS,cAAc,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAOO,SAAS,SAAS,GAAG;IAC1B,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,OAAO,SAAS;QACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IACzB,IAAI;AACN;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAQO,SAAS,WAAW,IAAI,EAAE,SAAS,OAAO;IAC/C,OAAO,IAAI,KAAK,cAAc,CAAC,QAAQ;QACrC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAQO,SAAS,qBAAqB,IAAI,EAAE,MAAM,GAAG;IAClD,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\nconst buttonVariants = {\n  default: \"bg-primary text-background hover:bg-primary/90 glow-primary\",\n  secondary: \"bg-secondary text-background hover:bg-secondary/90 glow-secondary\",\n  accent: \"bg-accent text-background hover:bg-accent/90 glow-accent\",\n  outline: \"border border-primary text-primary hover:bg-primary hover:text-background neon-border\",\n  ghost: \"text-primary hover:bg-primary/10 hover:text-primary\",\n  gradient: \"bg-gradient-to-r from-primary to-secondary text-background hover:from-primary/90 hover:to-secondary/90\",\n  glass: \"glass text-text hover:bg-white/20\"\n};\n\nconst buttonSizes = {\n  sm: \"h-9 px-3 text-sm\",\n  default: \"h-11 px-6 py-2\",\n  lg: \"h-12 px-8 text-lg\",\n  xl: \"h-14 px-10 text-xl\"\n};\n\nexport default function Button({\n  children,\n  className,\n  variant = \"default\",\n  size = \"default\",\n  disabled = false,\n  loading = false,\n  onClick,\n  type = \"button\",\n  ...props\n}) {\n  return (\n    <button\n      type={type}\n      className={cn(\n        // Base styles\n        \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300\",\n        \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\",\n        \"disabled:pointer-events-none disabled:opacity-50\",\n        \"transform hover:scale-105 active:scale-95\",\n        \n        // Variants\n        buttonVariants[variant],\n        \n        // Sizes\n        buttonSizes[size],\n        \n        // Loading state\n        loading && \"cursor-not-allowed opacity-70\",\n        \n        // Custom className\n        className\n      )}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"mr-2 h-4 w-4 animate-spin\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n}\n\n// Specialized button components\nexport function PrimaryButton({ children, ...props }) {\n  return (\n    <Button variant=\"default\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function SecondaryButton({ children, ...props }) {\n  return (\n    <Button variant=\"secondary\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function GradientButton({ children, ...props }) {\n  return (\n    <Button variant=\"gradient\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function OutlineButton({ children, ...props }) {\n  return (\n    <Button variant=\"outline\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\nexport function GlassButton({ children, ...props }) {\n  return (\n    <Button variant=\"glass\" {...props}>\n      {children}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,QAAQ;IACR,SAAS;IACT,OAAO;IACP,UAAU;IACV,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,OAAO,EAC7B,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,8FACA,0GACA,oDACA,6CAEA,WAAW;QACX,cAAc,CAAC,QAAQ,EAEvB,QAAQ;QACR,WAAW,CAAC,KAAK,EAEjB,gBAAgB;QAChB,WAAW,iCAEX,mBAAmB;QACnB;QAEF,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClD,qBACE,8OAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAChC;;;;;;AAGP;AAEO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,GAAG,OAAO;IACpD,qBACE,8OAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAClC;;;;;;AAGP;AAEO,SAAS,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO;IACnD,qBACE,8OAAC;QAAO,SAAQ;QAAY,GAAG,KAAK;kBACjC;;;;;;AAGP;AAEO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClD,qBACE,8OAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAChC;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChD,qBACE,8OAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAC9B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'Pitch', href: '/pitch' },\n    { name: 'Why Us', href: '/why-us' },\n    { name: 'Roadmap', href: '/roadmap' },\n  ];\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled \n          ? 'glass backdrop-blur-md border-b border-white/10' \n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300\">\n              <svg\n                className=\"w-6 h-6 text-background\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-bold font-display text-gradient\">\n              NameCardAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-text-muted hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full transition-all duration-300\" />\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"gradient\" size=\"sm\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2 rounded-lg text-text-muted hover:text-primary transition-colors\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMobileMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 glass backdrop-blur-md border-b border-white/10\">\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block text-text-muted hover:text-primary transition-colors duration-300 font-medium py-2\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 space-y-3\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">\n                  Sign In\n                </Button>\n                <Button variant=\"gradient\" size=\"sm\" className=\"w-full\">\n                  Get Started\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,oDACA;kBAGN,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;kDAER,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAK,WAAU;8CAA+C;;;;;;;;;;;;sCAMjE,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAW,MAAK;8CAAK;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,oBAAoB,CAAC;sCAEpC,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,iCACC,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,kCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAAS;;;;;;kDAGrD,8OAAC,iIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAW,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Button, { GradientButton, OutlineButton } from '@/components/ui/Button';\nimport { cn } from '@/lib/utils';\n\nexport default function HeroSection() {\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isDeleting, setIsDeleting] = useState(false);\n  \n  const phrases = [\n    'Your Name. Reinvented.',\n    'Not Just a Card—An Experience.',\n    'Connect in 3D. Remember Forever.',\n    'The Future of Networking Is Here.'\n  ];\n\n  useEffect(() => {\n    const currentPhrase = phrases[currentIndex];\n    const timeout = setTimeout(() => {\n      if (!isDeleting) {\n        if (typedText.length < currentPhrase.length) {\n          setTypedText(currentPhrase.slice(0, typedText.length + 1));\n        } else {\n          setTimeout(() => setIsDeleting(true), 2000);\n        }\n      } else {\n        if (typedText.length > 0) {\n          setTypedText(typedText.slice(0, -1));\n        } else {\n          setIsDeleting(false);\n          setCurrentIndex((prev) => (prev + 1) % phrases.length);\n        }\n      }\n    }, isDeleting ? 50 : 100);\n\n    return () => clearTimeout(timeout);\n  }, [typedText, currentIndex, isDeleting, phrases]);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Animated Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-background via-surface to-background\">\n        {/* Matrix Effect Background */}\n        <div className=\"absolute inset-0 opacity-20\">\n          {[...Array(50)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute text-accent text-xs font-mono animate-matrix\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 20}s`,\n                animationDuration: `${15 + Math.random() * 10}s`\n              }}\n            >\n              {Math.random().toString(36).substring(2, 15)}\n            </div>\n          ))}\n        </div>\n\n        {/* Floating Particles */}\n        <div className=\"absolute inset-0\">\n          {[...Array(20)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute w-2 h-2 bg-primary rounded-full animate-float opacity-60\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 3}s`,\n                animationDuration: `${3 + Math.random() * 2}s`\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Text Content */}\n          <div className=\"space-y-8\">\n            {/* Main Headline */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold font-display\">\n                <span className=\"text-gradient\">AR-Enhanced</span>\n                <br />\n                <span className=\"text-text\">Digital Business</span>\n                <br />\n                <span className=\"text-gradient-secondary\">Cards</span>\n              </h1>\n              \n              {/* Typing Animation */}\n              <div className=\"h-16 flex items-center justify-center lg:justify-start\">\n                <p className=\"text-xl md:text-2xl text-text-muted font-medium\">\n                  {typedText}\n                  <span className=\"animate-pulse\">|</span>\n                </p>\n              </div>\n            </div>\n\n            {/* Description */}\n            <p className=\"text-lg md:text-xl text-text-muted max-w-2xl mx-auto lg:mx-0 leading-relaxed\">\n              Revolutionize professional networking with stunning, interactive profiles \n              shared via QR, NFC, facial recognition, or camera scan—\n              <span className=\"text-primary font-semibold\">no app required</span>.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <GradientButton size=\"lg\" className=\"group\">\n                <span className=\"mr-2\">🚀</span>\n                Try Demo Now\n                <svg\n                  className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M13 7l5 5m0 0l-5 5m5-5H6\"\n                  />\n                </svg>\n              </GradientButton>\n              \n              <OutlineButton size=\"lg\" className=\"group\">\n                <span className=\"mr-2\">📹</span>\n                Watch Video\n                <svg\n                  className=\"ml-2 w-5 h-5 group-hover:scale-110 transition-transform\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M8 5v14l11-7z\" />\n                </svg>\n              </OutlineButton>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 pt-8 border-t border-surface-light\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-gradient\">50K+</div>\n                <div className=\"text-sm text-text-muted\">Cards Created</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-gradient\">12K+</div>\n                <div className=\"text-sm text-text-muted\">Active Users</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-gradient\">250K+</div>\n                <div className=\"text-sm text-text-muted\">Connections</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - 3D Card Preview */}\n          <div className=\"relative\">\n            {/* 3D Card Container */}\n            <div className=\"relative w-full max-w-md mx-auto\">\n              {/* Floating AR Card */}\n              <div className=\"relative transform rotate-12 hover:rotate-0 transition-transform duration-700 group\">\n                <div className=\"glass p-8 rounded-2xl neon-border glow-primary animate-float\">\n                  {/* Card Content */}\n                  <div className=\"text-center space-y-4\">\n                    {/* Avatar */}\n                    <div className=\"w-20 h-20 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-2xl font-bold text-background\">\n                      AI\n                    </div>\n                    \n                    {/* Name */}\n                    <h3 className=\"text-xl font-bold text-gradient\">Alex Thompson</h3>\n                    <p className=\"text-text-muted\">Senior Developer</p>\n                    <p className=\"text-sm text-text-dim\">TechFlow Inc.</p>\n                    \n                    {/* Contact Info */}\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex items-center justify-center space-x-2\">\n                        <span>📧</span>\n                        <span className=\"text-text-muted\"><EMAIL></span>\n                      </div>\n                      <div className=\"flex items-center justify-center space-x-2\">\n                        <span>📱</span>\n                        <span className=\"text-text-muted\">+1 (555) 123-4567</span>\n                      </div>\n                    </div>\n                    \n                    {/* AR Effect Indicator */}\n                    <div className=\"pt-4 border-t border-surface-light\">\n                      <div className=\"flex items-center justify-center space-x-2 text-accent\">\n                        <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                        <span className=\"text-xs\">AR Effect Active</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl -z-10 group-hover:blur-2xl transition-all duration-700\"></div>\n              </div>\n              \n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 w-8 h-8 bg-accent rounded-full animate-pulse\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-secondary rounded-full animate-float\"></div>\n              <div className=\"absolute top-1/2 -left-8 w-4 h-4 bg-primary rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-primary rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU;QACd;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,OAAO,CAAC,aAAa;QAC3C,MAAM,UAAU,WAAW;YACzB,IAAI,CAAC,YAAY;gBACf,IAAI,UAAU,MAAM,GAAG,cAAc,MAAM,EAAE;oBAC3C,aAAa,cAAc,KAAK,CAAC,GAAG,UAAU,MAAM,GAAG;gBACzD,OAAO;oBACL,WAAW,IAAM,cAAc,OAAO;gBACxC;YACF,OAAO;gBACL,IAAI,UAAU,MAAM,GAAG,GAAG;oBACxB,aAAa,UAAU,KAAK,CAAC,GAAG,CAAC;gBACnC,OAAO;oBACL,cAAc;oBACd,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;gBACvD;YACF;QACF,GAAG,aAAa,KAAK;QAErB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAW;QAAc;QAAY;KAAQ;IAEjD,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,gBAAgB,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oCACxC,mBAAmB,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;gCAClD;0CAEC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;+BARpC;;;;;;;;;;kCAcX,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gCAChD;+BAPK;;;;;;;;;;;;;;;;0BAcb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;oDACV;kEACD,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,8OAAC;oCAAE,WAAU;;wCAA+E;sDAG1F,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAAsB;;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,iBAAc;4CAAC,MAAK;4CAAK,WAAU;;8DAClC,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;8DAEhC,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;sDAKR,8OAAC,iIAAA,CAAA,gBAAa;4CAAC,MAAK;4CAAK,WAAU;;8DACjC,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;8DAEhC,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;8DAER,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAMd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAEb,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;sEAAgJ;;;;;;sEAK/J,8OAAC;4DAAG,WAAU;sEAAkC;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAAkB;;;;;;sEAC/B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEAGrC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,WAAU;sFAAkB;;;;;;;;;;;;8EAEpC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,WAAU;sFAAkB;;;;;;;;;;;;;;;;;;sEAKtC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOlC,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}]}