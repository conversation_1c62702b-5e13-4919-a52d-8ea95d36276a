'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function SharingMethodsDemo() {
  const [activeMethod, setActiveMethod] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [qrGenerated, setQrGenerated] = useState(false);
  const [nfcActive, setNfcActive] = useState(false);
  const [cameraActive, setCameraActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const sectionRef = useRef(null);

  const sharingMethods = [
    {
      id: 'qr',
      name: 'QR Code',
      icon: '📱',
      description: 'Generate instant QR codes for quick sharing',
      color: 'primary'
    },
    {
      id: 'nfc',
      name: 'NFC Tap',
      icon: '📡',
      description: 'Near Field Communication for contactless sharing',
      color: 'secondary'
    },
    {
      id: 'camera',
      name: 'Camera Scan',
      icon: '📷',
      description: 'AI-powered visual recognition technology',
      color: 'accent'
    },
    {
      id: 'search',
      name: 'Name Search',
      icon: '🔍',
      description: 'Find cards by name or company instantly',
      color: 'primary'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleMethodClick = (index) => {
    setActiveMethod(index);
    // Reset all states
    setQrGenerated(false);
    setNfcActive(false);
    setCameraActive(false);
    setSearchQuery('');
  };

  const generateQR = () => {
    setQrGenerated(true);
    setTimeout(() => setQrGenerated(false), 3000);
  };

  const activateNFC = () => {
    setNfcActive(true);
    setTimeout(() => setNfcActive(false), 3000);
  };

  const activateCamera = () => {
    setCameraActive(true);
    setTimeout(() => setCameraActive(false), 3000);
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  return (
    <section ref={sectionRef} className="py-20 bg-background relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 30% 70%, var(--secondary) 0%, transparent 50%), 
                           radial-gradient(circle at 70% 30%, var(--accent) 0%, transparent 50%)`,
          backgroundSize: '300px 300px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">Sharing Methods</span>
            <br />
            <span className="text-text">Multiple Ways to Connect</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto">
            Experience all the ways you can share your AR business card. 
            From QR codes to AI-powered recognition, we've got you covered.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Method Selection */}
          <div className={cn(
            "space-y-6 transition-all duration-700",
            isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-10"
          )}>
            <h3 className="text-2xl font-bold text-gradient mb-6">
              Choose a Sharing Method
            </h3>

            {sharingMethods.map((method, index) => (
              <button
                key={method.id}
                onClick={() => handleMethodClick(index)}
                className={cn(
                  "w-full p-6 rounded-xl border transition-all duration-300 text-left group",
                  activeMethod === index
                    ? "border-primary bg-primary/10 scale-105"
                    : "border-surface-light bg-surface hover:border-primary/50 hover:scale-102"
                )}
              >
                <div className="flex items-center space-x-4">
                  <div className={cn(
                    "text-4xl p-3 rounded-lg transition-all duration-300",
                    activeMethod === index
                      ? "bg-gradient-to-r from-primary to-secondary"
                      : "bg-surface-light group-hover:bg-primary/20"
                  )}>
                    {method.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className={cn(
                      "text-xl font-semibold mb-2 transition-colors duration-300",
                      activeMethod === index ? "text-gradient" : "text-text"
                    )}>
                      {method.name}
                    </h4>
                    <p className="text-text-muted">
                      {method.description}
                    </p>
                  </div>
                  <div className={cn(
                    "w-4 h-4 rounded-full transition-all duration-300",
                    activeMethod === index 
                      ? "bg-primary animate-pulse" 
                      : "bg-surface-light"
                  )} />
                </div>
              </button>
            ))}
          </div>

          {/* Demo Area */}
          <div className={cn(
            "transition-all duration-700",
            isVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-10"
          )}>
            <Card variant="glass" className="p-8 min-h-[500px] flex flex-col justify-center">
              {/* QR Code Demo */}
              {activeMethod === 0 && (
                <div className="text-center space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">QR Code Generator</h4>
                  
                  <div className="relative">
                    <div className={cn(
                      "w-48 h-48 mx-auto border-2 border-dashed rounded-lg flex items-center justify-center transition-all duration-500",
                      qrGenerated 
                        ? "border-primary bg-primary/10" 
                        : "border-surface-light bg-surface"
                    )}>
                      {qrGenerated ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-8 gap-1">
                            {[...Array(64)].map((_, i) => (
                              <div
                                key={i}
                                className={cn(
                                  "w-2 h-2 rounded-sm",
                                  Math.random() > 0.5 ? "bg-primary" : "bg-transparent"
                                )}
                              />
                            ))}
                          </div>
                          <div className="text-sm text-primary font-medium">QR Code Generated!</div>
                        </div>
                      ) : (
                        <div className="text-text-muted">
                          <div className="text-4xl mb-2">📱</div>
                          <div>Click to generate</div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Button 
                    variant="gradient" 
                    onClick={generateQR}
                    disabled={qrGenerated}
                  >
                    {qrGenerated ? 'Generated!' : 'Generate QR Code'}
                  </Button>
                  
                  <p className="text-sm text-text-muted">
                    Scan with any smartphone camera to view the AR card
                  </p>
                </div>
              )}

              {/* NFC Demo */}
              {activeMethod === 1 && (
                <div className="text-center space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">NFC Sharing</h4>
                  
                  <div className="relative">
                    <div className={cn(
                      "w-32 h-32 mx-auto rounded-full border-4 flex items-center justify-center transition-all duration-500",
                      nfcActive 
                        ? "border-secondary bg-secondary/20 animate-pulse-glow" 
                        : "border-surface-light bg-surface"
                    )}>
                      <div className="text-4xl">📡</div>
                    </div>
                    
                    {nfcActive && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-40 h-40 border-2 border-secondary rounded-full animate-ping opacity-75"></div>
                      </div>
                    )}
                  </div>
                  
                  <Button 
                    variant="gradient" 
                    onClick={activateNFC}
                    disabled={nfcActive}
                  >
                    {nfcActive ? 'NFC Active!' : 'Activate NFC'}
                  </Button>
                  
                  <p className="text-sm text-text-muted">
                    Tap NFC-enabled devices to instantly share your card
                  </p>
                </div>
              )}

              {/* Camera Scan Demo */}
              {activeMethod === 2 && (
                <div className="text-center space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">Camera Recognition</h4>
                  
                  <div className="relative">
                    <div className={cn(
                      "w-48 h-36 mx-auto border-2 rounded-lg flex items-center justify-center transition-all duration-500",
                      cameraActive 
                        ? "border-accent bg-accent/10" 
                        : "border-surface-light bg-surface"
                    )}>
                      {cameraActive ? (
                        <div className="space-y-2">
                          <div className="text-4xl animate-pulse">👤</div>
                          <div className="text-sm text-accent">Face Detected!</div>
                          <div className="w-24 h-1 bg-accent rounded-full animate-pulse"></div>
                        </div>
                      ) : (
                        <div className="text-text-muted">
                          <div className="text-4xl mb-2">📷</div>
                          <div>Camera viewfinder</div>
                        </div>
                      )}
                    </div>
                    
                    {cameraActive && (
                      <div className="absolute inset-0 border-2 border-accent rounded-lg animate-pulse"></div>
                    )}
                  </div>
                  
                  <Button 
                    variant="gradient" 
                    onClick={activateCamera}
                    disabled={cameraActive}
                  >
                    {cameraActive ? 'Scanning...' : 'Start Camera Scan'}
                  </Button>
                  
                  <p className="text-sm text-text-muted">
                    AI recognizes faces and displays associated business cards
                  </p>
                </div>
              )}

              {/* Name Search Demo */}
              {activeMethod === 3 && (
                <div className="text-center space-y-6">
                  <h4 className="text-2xl font-bold text-gradient">Name Search</h4>
                  
                  <div className="space-y-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search by name or company..."
                        value={searchQuery}
                        onChange={(e) => handleSearch(e.target.value)}
                        className="w-full px-4 py-3 bg-background border border-surface-light rounded-lg text-text placeholder-text-dim focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted">
                        🔍
                      </div>
                    </div>
                    
                    {searchQuery && (
                      <div className="space-y-2 text-left">
                        <div className="p-3 bg-surface rounded-lg border border-primary/30 hover:border-primary/50 transition-colors cursor-pointer">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-sm font-bold text-background">
                              A
                            </div>
                            <div>
                              <div className="font-medium text-text">Alex Thompson</div>
                              <div className="text-sm text-text-muted">Senior Developer at TechFlow</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="p-3 bg-surface rounded-lg border border-surface-light hover:border-primary/30 transition-colors cursor-pointer">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-secondary to-accent rounded-full flex items-center justify-center text-sm font-bold text-background">
                              M
                            </div>
                            <div>
                              <div className="font-medium text-text">Maria Garcia</div>
                              <div className="text-sm text-text-muted">UX Designer at Creative Studio</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <p className="text-sm text-text-muted">
                    Find any card instantly by typing a name or company
                  </p>
                </div>
              )}
            </Card>
          </div>
        </div>

        {/* Features Comparison */}
        <div className="mt-20">
          <Card variant="glass" className="p-8">
            <h3 className="text-2xl font-bold text-gradient text-center mb-8">
              Why Multiple Sharing Methods Matter
            </h3>
            
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl mb-3">⚡</div>
                <h4 className="font-semibold text-text mb-2">Instant Access</h4>
                <p className="text-sm text-text-muted">No app downloads or account creation required</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-3">🌐</div>
                <h4 className="font-semibold text-text mb-2">Universal Compatibility</h4>
                <p className="text-sm text-text-muted">Works on any device with a web browser</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-3">🔒</div>
                <h4 className="font-semibold text-text mb-2">Privacy Focused</h4>
                <p className="text-sm text-text-muted">You control who sees your card and when</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-3">📊</div>
                <h4 className="font-semibold text-text mb-2">Analytics Included</h4>
                <p className="text-sm text-text-muted">Track views and engagement in real-time</p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
}
