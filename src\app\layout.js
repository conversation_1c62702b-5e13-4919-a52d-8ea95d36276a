import { Inter, Orbitron } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  display: "swap",
});

export const metadata = {
  title: "NameCardAI - AR-Enhanced Digital Business Cards",
  description: "Revolutionize professional networking with stunning, interactive AR business cards. Share via QR, NFC, camera scan, or name recognition—no app required.",
  keywords: "digital business cards, AR business cards, networking, QR code, NFC, professional networking, 3D cards",
  authors: [{ name: "NameCardAI Team" }],
  creator: "<PERSON><PERSON>ard<PERSON><PERSON>",
  publisher: "NameCardAI",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
    shortcut: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
    apple: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
  },
  openGraph: {
    title: "NameCardAI - AR-Enhanced Digital Business Cards",
    description: "The future of networking is here. Create stunning AR business cards that work without apps.",
    url: "https://namecardai.com",
    siteName: "NameCardAI",
    images: [
      {
        url: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
        width: 1200,
        height: 630,
        alt: "NameCardAI - AR Business Cards",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NameCardAI - AR-Enhanced Digital Business Cards",
    description: "The future of networking is here. Create stunning AR business cards that work without apps.",
    images: ["https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${inter.variable} ${orbitron.variable} antialiased bg-background text-text min-h-screen`}
      >
        {children}
      </body>
    </html>
  );
}
