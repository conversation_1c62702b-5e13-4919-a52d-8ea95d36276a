'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import Button, { GradientButton } from '@/components/ui/Button';

export default function ProblemSolutionSection() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const problems = [
    {
      icon: '📄',
      title: 'Paper Cards Are Outdated',
      description: 'Get lost, damaged, or thrown away within a week. 88% end up in trash.',
      stat: '7B+ cards wasted yearly'
    },
    {
      icon: '🔋',
      title: 'Tech Dependency Issues',
      description: 'QR/NFC cards rely on battery life, app compatibility, and device support.',
      stat: '40% failure rate'
    },
    {
      icon: '🤝',
      title: 'Missed Connections',
      description: 'Forgetting cards or low-tech limitations lead to lost networking opportunities.',
      stat: '60% of contacts lost'
    },
    {
      icon: '💼',
      title: 'No Digital Engagement',
      description: 'Physical cards offer zero interaction, analytics, or follow-up capabilities.',
      stat: 'Zero insights'
    }
  ];

  const solutions = [
    {
      icon: '🎯',
      title: 'AR-Powered Cards',
      description: 'Stunning 3D animated business cards with real-time AR effects that wow.',
      benefit: 'Instant memorability'
    },
    {
      icon: '📱',
      title: 'Multiple Share Methods',
      description: 'QR, NFC, camera scan, or just remember a name/number. Always accessible.',
      benefit: 'Never miss a connection'
    },
    {
      icon: '🌐',
      title: 'No App Required',
      description: 'Works directly in any web browser. Recipients need zero downloads.',
      benefit: '100% compatibility'
    },
    {
      icon: '📊',
      title: 'Smart Analytics',
      description: 'Track views, interactions, and networking success with detailed insights.',
      benefit: 'Measurable ROI'
    }
  ];

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-background to-surface relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-30">
        {/* Floating geometric shapes */}
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "absolute rounded-full opacity-20",
              i % 3 === 0 ? "bg-primary" : i % 3 === 1 ? "bg-secondary" : "bg-accent"
            )}
            style={{
              width: `${20 + Math.random() * 40}px`,
              height: `${20 + Math.random() * 40}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">The Problem</span>
            <span className="text-text"> vs </span>
            <span className="text-gradient-secondary">Our Solution</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto">
            Traditional networking is broken. We're fixing it with cutting-edge AR technology 
            that makes every interaction unforgettable.
          </p>
        </div>

        {/* Problem vs Solution Grid */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20">
          {/* Problems Side */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-3xl font-bold text-gradient mb-4 flex items-center justify-center lg:justify-start">
                <span className="mr-3">⚠️</span>
                Current Problems
              </h3>
              <p className="text-text-muted">
                Why traditional business cards and current digital solutions fail
              </p>
            </div>

            <div className="space-y-6">
              {problems.map((problem, index) => (
                <div
                  key={index}
                  className={cn(
                    "glass p-6 rounded-xl border border-red-500/20 transition-all duration-700",
                    isVisible 
                      ? "opacity-100 translate-x-0" 
                      : "opacity-0 -translate-x-10"
                  )}
                  style={{ transitionDelay: `${index * 150}ms` }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-3xl">{problem.icon}</div>
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-text mb-2">
                        {problem.title}
                      </h4>
                      <p className="text-text-muted mb-3">
                        {problem.description}
                      </p>
                      <div className="inline-block bg-red-500/20 text-red-400 px-3 py-1 rounded-full text-sm font-medium">
                        {problem.stat}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Solutions Side */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-3xl font-bold text-gradient-secondary mb-4 flex items-center justify-center lg:justify-start">
                <span className="mr-3">✨</span>
                Our Solutions
              </h3>
              <p className="text-text-muted">
                How NameCardAI revolutionizes professional networking
              </p>
            </div>

            <div className="space-y-6">
              {solutions.map((solution, index) => (
                <div
                  key={index}
                  className={cn(
                    "glass p-6 rounded-xl neon-border glow-primary transition-all duration-700 hover:scale-105",
                    isVisible 
                      ? "opacity-100 translate-x-0" 
                      : "opacity-0 translate-x-10"
                  )}
                  style={{ transitionDelay: `${index * 150 + 200}ms` }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-3xl">{solution.icon}</div>
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-text mb-2">
                        {solution.title}
                      </h4>
                      <p className="text-text-muted mb-3">
                        {solution.description}
                      </p>
                      <div className="inline-block bg-gradient-to-r from-primary/20 to-secondary/20 text-primary px-3 py-1 rounded-full text-sm font-medium">
                        {solution.benefit}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Comparison Stats */}
        <div className="mt-20 text-center">
          <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gradient mb-8">
              The Difference is Clear
            </h3>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-red-400 mb-2">88%</div>
                <div className="text-text-muted">Paper cards thrown away</div>
                <div className="text-sm text-red-400 mt-1">Traditional</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-gradient mb-2">70%</div>
                <div className="text-text-muted">Higher engagement with AR</div>
                <div className="text-sm text-primary mt-1">NameCardAI</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-gradient-secondary mb-2">100%</div>
                <div className="text-text-muted">Browser compatibility</div>
                <div className="text-sm text-accent mt-1">No app needed</div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-text mb-4">
            Ready to revolutionize your networking?
          </h3>
          <p className="text-text-muted mb-8 max-w-2xl mx-auto">
            Join thousands of professionals who've already made the switch to AR-enhanced digital business cards.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton size="lg">
              <span className="mr-2">🚀</span>
              Start Free Trial
            </GradientButton>
            <Button variant="outline" size="lg">
              <span className="mr-2">📊</span>
              See Comparison
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
