import { cn } from "@/lib/utils";

const buttonVariants = {
  default: "bg-primary text-background hover:bg-primary/90 glow-primary",
  secondary: "bg-secondary text-background hover:bg-secondary/90 glow-secondary",
  accent: "bg-accent text-background hover:bg-accent/90 glow-accent",
  outline: "border border-primary text-primary hover:bg-primary hover:text-background neon-border",
  ghost: "text-primary hover:bg-primary/10 hover:text-primary",
  gradient: "bg-gradient-to-r from-primary to-secondary text-background hover:from-primary/90 hover:to-secondary/90",
  glass: "glass text-text hover:bg-white/20"
};

const buttonSizes = {
  sm: "h-9 px-3 text-sm",
  default: "h-11 px-6 py-2",
  lg: "h-12 px-8 text-lg",
  xl: "h-14 px-10 text-xl"
};

export default function Button({
  children,
  className,
  variant = "default",
  size = "default",
  disabled = false,
  loading = false,
  onClick,
  type = "button",
  ...props
}) {
  return (
    <button
      type={type}
      className={cn(
        // Base styles
        "inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",
        "transform hover:scale-105 active:scale-95",
        
        // Variants
        buttonVariants[variant],
        
        // Sizes
        buttonSizes[size],
        
        // Loading state
        loading && "cursor-not-allowed opacity-70",
        
        // Custom className
        className
      )}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <svg
          className="mr-2 h-4 w-4 animate-spin"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
}

// Specialized button components
export function PrimaryButton({ children, ...props }) {
  return (
    <Button variant="default" {...props}>
      {children}
    </Button>
  );
}

export function SecondaryButton({ children, ...props }) {
  return (
    <Button variant="secondary" {...props}>
      {children}
    </Button>
  );
}

export function GradientButton({ children, ...props }) {
  return (
    <Button variant="gradient" {...props}>
      {children}
    </Button>
  );
}

export function OutlineButton({ children, ...props }) {
  return (
    <Button variant="outline" {...props}>
      {children}
    </Button>
  );
}

export function GlassButton({ children, ...props }) {
  return (
    <Button variant="glass" {...props}>
      {children}
    </Button>
  );
}
