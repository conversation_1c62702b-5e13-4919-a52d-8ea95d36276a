"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),n=require("react"),t=require("@react-three/fiber"),r=require("three"),o=require("three-stdlib"),u=require("./useEnvironment.cjs.js");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js");var s=a(e),c=i(n);function l(e,n,r,o,u={}){var a,i,s,c;u={backgroundBlurriness:0,backgroundIntensity:1,backgroundRotation:[0,0,0],environmentIntensity:1,environmentRotation:[0,0,0],...u};const l=(e=>{return(n=e).current&&n.current.isScene?e.current:e;var n})(n||r),d=l.background,m=l.environment,g={backgroundBlurriness:l.backgroundBlurriness,backgroundIntensity:l.backgroundIntensity,backgroundRotation:null!==(a=null==(i=l.backgroundRotation)||null==i.clone?void 0:i.clone())&&void 0!==a?a:[0,0,0],environmentIntensity:l.environmentIntensity,environmentRotation:null!==(s=null==(c=l.environmentRotation)||null==c.clone?void 0:c.clone())&&void 0!==s?s:[0,0,0]};return"only"!==e&&(l.environment=o),e&&(l.background=o),t.applyProps(l,u),()=>{"only"!==e&&(l.environment=m),e&&(l.background=d),t.applyProps(l,g)}}function d({scene:e,background:n=!1,map:r,...o}){const u=t.useThree((e=>e.scene));return c.useLayoutEffect((()=>{if(r)return l(n,e,u,r,o)})),null}function m({background:e=!1,scene:n,blur:r,backgroundBlurriness:o,backgroundIntensity:a,backgroundRotation:i,environmentIntensity:s,environmentRotation:d,...m}){const g=u.useEnvironment(m),b=t.useThree((e=>e.scene));return c.useLayoutEffect((()=>l(e,n,b,g,{backgroundBlurriness:null!=r?r:o,backgroundIntensity:a,backgroundRotation:i,environmentIntensity:s,environmentRotation:d}))),c.useEffect((()=>()=>{g.dispose()}),[g]),null}function g({children:e,near:n=.1,far:o=1e3,resolution:u=256,frames:a=1,map:i,background:s=!1,blur:g,backgroundBlurriness:b,backgroundIntensity:f,backgroundRotation:v,environmentIntensity:p,environmentRotation:k,scene:E,files:y,path:h,preset:I,extensions:R}){const j=t.useThree((e=>e.gl)),x=t.useThree((e=>e.scene)),P=c.useRef(null),[C]=c.useState((()=>new r.Scene)),q=c.useMemo((()=>{const e=new r.WebGLCubeRenderTarget(u);return e.texture.type=r.HalfFloatType,e}),[u]);c.useEffect((()=>()=>{q.dispose()}),[q]),c.useLayoutEffect((()=>{if(1===a){const e=j.autoClear;j.autoClear=!0,P.current.update(j,C),j.autoClear=e}return l(s,E,x,q.texture,{backgroundBlurriness:null!=g?g:b,backgroundIntensity:f,backgroundRotation:v,environmentIntensity:p,environmentRotation:k})}),[e,C,q.texture,E,x,s,a,j]);let B=1;return t.useFrame((()=>{if(a===1/0||B<a){const e=j.autoClear;j.autoClear=!0,P.current.update(j,C),j.autoClear=e,B++}})),c.createElement(c.Fragment,null,t.createPortal(c.createElement(c.Fragment,null,e,c.createElement("cubeCamera",{ref:P,args:[n,o,q]}),y||I?c.createElement(m,{background:!0,files:y,preset:I,path:h,extensions:R}):i?c.createElement(d,{background:!0,map:i,extensions:R}):null),C))}function b(e){var n,r,a,i;const l=u.useEnvironment(e),m=e.map||l;c.useMemo((()=>t.extend({GroundProjectedEnvImpl:o.GroundProjectedEnv})),[]),c.useEffect((()=>()=>{l.dispose()}),[l]);const g=c.useMemo((()=>[m]),[m]),b=null==(n=e.ground)?void 0:n.height,f=null==(r=e.ground)?void 0:r.radius,v=null!==(a=null==(i=e.ground)?void 0:i.scale)&&void 0!==a?a:1e3;return c.createElement(c.Fragment,null,c.createElement(d,s.default({},e,{map:m})),c.createElement("groundProjectedEnvImpl",{args:g,scale:v,height:b,radius:f}))}exports.Environment=function(e){return e.ground?c.createElement(b,e):e.map?c.createElement(d,e):e.children?c.createElement(g,e):c.createElement(m,e)},exports.EnvironmentCube=m,exports.EnvironmentMap=d,exports.EnvironmentPortal=g;
