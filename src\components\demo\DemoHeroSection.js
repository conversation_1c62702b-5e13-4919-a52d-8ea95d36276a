'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import Button, { GradientButton } from '@/components/ui/Button';

export default function DemoHeroSection() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-surface to-background">
        {/* Holographic Grid */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `linear-gradient(rgba(0, 245, 255, 0.3) 1px, transparent 1px), 
                             linear-gradient(90deg, rgba(0, 245, 255, 0.3) 1px, transparent 1px)`,
            backgroundSize: '50px 50px',
            animation: 'matrix 30s linear infinite'
          }} />
        </div>

        {/* Floating AR Elements */}
        <div className="absolute inset-0">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className={cn(
                "absolute rounded-lg opacity-60 animate-float",
                i % 3 === 0 ? "bg-primary/20 border border-primary/50" : 
                i % 3 === 1 ? "bg-secondary/20 border border-secondary/50" : 
                "bg-accent/20 border border-accent/50"
              )}
              style={{
                width: `${30 + Math.random() * 40}px`,
                height: `${20 + Math.random() * 30}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${4 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className={cn(
          "transition-all duration-1000",
          isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        )}>
          {/* Main Headline */}
          <div className="space-y-6 mb-12">
            <div className="inline-block bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full px-6 py-2 border border-primary/30">
              <span className="text-primary font-medium">🎯 Live Interactive Demo</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold font-display">
              <span className="text-gradient">Experience AR</span>
              <br />
              <span className="text-text">Business Cards</span>
              <br />
              <span className="text-gradient-secondary">In Action</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-text-muted max-w-4xl mx-auto leading-relaxed">
              Create, customize, and share stunning 3D digital business cards with real-time AR effects. 
              No downloads required—experience the future of networking right in your browser.
            </p>
          </div>

          {/* Demo Features Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="glass p-6 rounded-xl neon-border hover:scale-105 transition-transform duration-300">
              <div className="text-4xl mb-4">🎨</div>
              <h3 className="text-xl font-semibold text-gradient mb-2">Real-Time Customization</h3>
              <p className="text-text-muted">Design your card with live preview and instant updates</p>
            </div>
            
            <div className="glass p-6 rounded-xl neon-border hover:scale-105 transition-transform duration-300">
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-semibold text-gradient mb-2">AR Effects Library</h3>
              <p className="text-text-muted">Choose from 50+ stunning effects and animations</p>
            </div>
            
            <div className="glass p-6 rounded-xl neon-border hover:scale-105 transition-transform duration-300">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-xl font-semibold text-gradient mb-2">Multi-Share Options</h3>
              <p className="text-text-muted">QR, NFC, camera scan, or name-based sharing</p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <GradientButton size="xl" className="group">
              <span className="mr-3 text-2xl">🎮</span>
              Start Creating Now
              <svg
                className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7l5 5m0 0l-5 5m5-5H6"
                />
              </svg>
            </GradientButton>
            
            <Button variant="outline" size="xl" className="group">
              <span className="mr-3 text-2xl">👁️</span>
              Watch Tutorial
              <svg
                className="ml-3 w-6 h-6 group-hover:scale-110 transition-transform"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </Button>
          </div>

          {/* Demo Stats */}
          <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gradient mb-6">
              Join the AR Revolution
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient mb-2">50K+</div>
                <div className="text-text-muted text-sm">Cards Created</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient mb-2">98%</div>
                <div className="text-text-muted text-sm">User Satisfaction</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient mb-2">45+</div>
                <div className="text-text-muted text-sm">Countries</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient mb-2">24/7</div>
                <div className="text-text-muted text-sm">Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="flex flex-col items-center space-y-2">
          <div className="text-text-muted text-sm">Try the demo below</div>
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
