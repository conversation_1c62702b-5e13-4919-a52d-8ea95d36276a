import Header from '@/components/layout/Header';
import DemoHeroSection from '@/components/demo/DemoHeroSection';
import ARCardSimulator from '@/components/demo/ARCardSimulator';
import SharingMethodsDemo from '@/components/demo/SharingMethodsDemo';
import InteractiveFeaturesDemo from '@/components/demo/InteractiveFeaturesDemo';
import Footer from '@/components/layout/Footer';

export const metadata = {
  title: "Live Demo - NameCardAI AR Business Cards",
  description: "Experience the future of networking with our interactive AR business card demo. Create, customize, and share stunning 3D digital business cards.",
  keywords: "AR business card demo, 3D business card, digital networking demo, interactive business card",
};

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <DemoHeroSection />
        <ARCardSimulator />
        <SharingMethodsDemo />
        <InteractiveFeaturesDemo />
      </main>
      <Footer />
    </div>
  );
}
