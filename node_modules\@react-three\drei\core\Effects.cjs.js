"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),a=require("@react-three/fiber"),n=require("three-stdlib");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=s(e),l=u(r);const o=l.forwardRef((({children:e,multisamping:r=8,renderIndex:s=1,disableRender:u,disableGamma:o,disableRenderPass:i,depthBuffer:d=!0,stencilBuffer:f=!1,anisotropy:p=1,colorSpace:h,type:m,...b},g)=>{l.useMemo((()=>a.extend({EffectComposer:n.EffectComposer,RenderPass:n.RenderPass,ShaderPass:n.ShaderPass})),[]);const y=l.useRef(null);l.useImperativeHandle(g,(()=>y.current),[]);const{scene:v,camera:E,gl:P,size:x,viewport:R}=a.useThree(),[w]=l.useState((()=>{const e=new t.WebGLRenderTarget(x.width,x.height,{type:m||t.HalfFloatType,format:t.RGBAFormat,depthBuffer:d,stencilBuffer:f,anisotropy:p});return m===t.UnsignedByteType&&null!=h&&(e.texture.colorSpace=h),e.samples=r,e}));l.useEffect((()=>{var e,r;null==(e=y.current)||e.setSize(x.width,x.height),null==(r=y.current)||r.setPixelRatio(R.dpr)}),[P,x,R.dpr]),a.useFrame((()=>{var e;u||null==(e=y.current)||e.render()}),s);const j=[];return i||j.push(l.createElement("renderPass",{key:"renderpass",attach:`passes-${j.length}`,args:[v,E]})),o||j.push(l.createElement("shaderPass",{attach:`passes-${j.length}`,key:"gammapass",args:[n.GammaCorrectionShader]})),l.Children.forEach(e,(e=>{e&&j.push(l.cloneElement(e,{key:j.length,attach:`passes-${j.length}`}))})),l.createElement("effectComposer",c.default({ref:y,args:[P,w]},b),j)}));exports.Effects=o,exports.isWebGL2Available=()=>{try{var e=document.createElement("canvas");return!(!window.WebGL2RenderingContext||!e.getContext("webgl2"))}catch(e){return!1}};
