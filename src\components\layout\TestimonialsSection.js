'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { testimonials, stats } from '@/lib/data';
import { TestimonialCard } from '@/components/ui/Card';

export default function TestimonialsSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const sectionRef = useRef(null);
  const intervalRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Start auto-rotation
          intervalRef.current = setInterval(() => {
            setActiveTestimonial((prev) => (prev + 1) % testimonials.filter(t => t.featured).length);
          }, 5000);
        } else {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const featuredTestimonials = testimonials.filter(t => t.featured);

  return (
    <section ref={sectionRef} className="py-20 bg-background relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-20">
        {/* Floating testimonial bubbles */}
        {[...Array(10)].map((_, i) => (
          <div
            key={i}
            className="absolute w-4 h-4 bg-primary/30 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-6">
            <span className="text-gradient">Loved by Professionals</span>
            <br />
            <span className="text-text">Worldwide</span>
          </h2>
          <p className="text-xl text-text-muted max-w-3xl mx-auto">
            Join thousands of professionals who've revolutionized their networking 
            with NameCardAI's AR-enhanced digital business cards.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div
              key={index}
              className={cn(
                "text-center transition-all duration-700",
                isVisible 
                  ? "opacity-100 translate-y-0" 
                  : "opacity-0 translate-y-10"
              )}
              style={{ transitionDelay: `${index * 150}ms` }}
            >
              <div className="glass p-6 rounded-xl hover:scale-105 transition-transform duration-300">
                <div className="text-4xl mb-2">{stat.icon}</div>
                <div className="text-3xl font-bold text-gradient mb-1">
                  {stat.value}
                </div>
                <div className="text-text-muted text-sm">
                  {stat.label}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Featured Testimonial Carousel */}
        <div className="mb-16">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Main Testimonial Display */}
              <div className="glass p-8 md:p-12 rounded-2xl neon-border glow-primary text-center">
                <div className={cn(
                  "transition-all duration-500",
                  isVisible ? "opacity-100 scale-100" : "opacity-0 scale-95"
                )}>
                  {/* Quote Icon */}
                  <div className="text-6xl text-primary/30 mb-6">"</div>
                  
                  {/* Testimonial Content */}
                  <blockquote className="text-xl md:text-2xl text-text mb-8 italic leading-relaxed">
                    {featuredTestimonials[activeTestimonial]?.content}
                  </blockquote>
                  
                  {/* Rating */}
                  <div className="flex justify-center mb-6">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-2xl text-accent">★</span>
                    ))}
                  </div>
                  
                  {/* Author Info */}
                  <div className="flex items-center justify-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center text-background font-bold text-xl">
                      {featuredTestimonials[activeTestimonial]?.name.charAt(0)}
                    </div>
                    <div className="text-left">
                      <div className="font-semibold text-text text-lg">
                        {featuredTestimonials[activeTestimonial]?.name}
                      </div>
                      <div className="text-text-muted">
                        {featuredTestimonials[activeTestimonial]?.role} at {featuredTestimonials[activeTestimonial]?.company}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Navigation Dots */}
              <div className="flex justify-center mt-8 space-x-3">
                {featuredTestimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={cn(
                      "w-3 h-3 rounded-full transition-all duration-300",
                      activeTestimonial === index 
                        ? "bg-primary w-8" 
                        : "bg-surface-light hover:bg-primary/50"
                    )}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* All Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={testimonial.id}
              name={testimonial.name}
              role={testimonial.role}
              company={testimonial.company}
              content={testimonial.content}
              avatar={testimonial.avatar}
              rating={testimonial.rating}
              className={cn(
                "transition-all duration-700",
                isVisible 
                  ? "opacity-100 translate-y-0" 
                  : "opacity-0 translate-y-10"
              )}
              style={{ transitionDelay: `${index * 150}ms` }}
            />
          ))}
        </div>

        {/* Social Proof Section */}
        <div className="mt-16 text-center">
          <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gradient mb-6">
              Trusted by Industry Leaders
            </h3>
            
            {/* Company Logos Placeholder */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60">
              {['TechCorp', 'InnovateLab', 'FutureWorks', 'DigitalFirst', 'NextGen'].map((company, index) => (
                <div
                  key={index}
                  className="text-center p-4 bg-surface rounded-lg border border-surface-light hover:border-primary/50 transition-colors"
                >
                  <div className="text-2xl mb-2">🏢</div>
                  <div className="text-sm text-text-muted font-medium">{company}</div>
                </div>
              ))}
            </div>
            
            <div className="mt-8 text-text-muted">
              Join 12,500+ professionals from leading companies worldwide
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-text mb-4">
              Ready to join them?
            </h3>
            <p className="text-text-muted mb-8">
              Start creating your AR-enhanced digital business card today and 
              experience the future of professional networking.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95 bg-gradient-to-r from-primary to-secondary text-background hover:from-primary/90 hover:to-secondary/90 h-12 px-8 text-lg">
                <span className="mr-2">🚀</span>
                Start Free Trial
              </button>
              <button className="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95 border border-primary text-primary hover:bg-primary hover:text-background neon-border h-12 px-8 text-lg">
                <span className="mr-2">👥</span>
                Join Community
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
